{"compilerOptions": {"types": ["node", "@testing-library/jest-dom/vitest", "vitest/globals"], "target": "ES2020", "lib": ["dom", "dom.iterable", "esnext", "ES2020"], "allowImportingTsExtensions": true, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"], "@pageClient/*": ["./src/entity/client/pages/*"]}}, "include": ["./src", "vite.config.ts"], "references": [{"path": "./tsconfig.node.json"}]}