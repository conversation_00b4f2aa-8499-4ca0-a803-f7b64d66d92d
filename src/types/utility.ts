import { Ref } from 'react';

export interface DefaultItemOption {
  label: string;
  value: string;
}

export interface RadioOption extends Omit<DefaultItemOption, 'value'> {
  value: string | boolean;
  disabled?: boolean;
}

export type SortOrderOptionType = 'asc' | 'desc';

export interface SortOptionsProps<T> {
  sortOrder: SortOrderOptionType;
  sortBy: keyof T;
}

export interface ApiErrorMessagesForm {
  message: string;
  field: string;
}

export type ApiErrorMessagesProps = {
  [k: string]: string[];
};

export interface ApiErrorProps {
  data: {
    errors: ApiErrorMessagesProps;
    message: string;
  };
  apiResponse: Response;
}

export interface SidebarTab {
  id: string;
  title: string;
  icon: () => JSX.Element;
  url: string;
  nestedLinks?: SidebarTab[];
}

export interface ErrorValidationType<T> {
  message: string;
  type: keyof T;
}

export interface ErrorValidationRootType<T> {
  root: {
    message: string;
    ref?: Ref<HTMLInputElement>;
    type: keyof T;
  };
}

export type ErrorValidationProps<T> =
  | ErrorValidationRootType<T>
  | ErrorValidationType<T>;

export interface GetDataWithPagination<T> {
  data: T[];
  meta: {
    total: number;
  };
}
