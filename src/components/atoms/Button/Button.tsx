import { MouseEvent } from 'react';
import {
  ActionIconProps,
  <PERSON>ton as MButton,
  ButtonProps as MButtonProps,
} from '@mantine/core';

import { ActionIcon } from '..';
import classNames from './button.module.css';

type ButtonColorVariant = 'primary' | 'secondary' | 'transparent';

interface ButtonProps extends MButtonProps {
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  iconOnly?: boolean;
  color?: ButtonColorVariant;
  dataTestId?: string;
}

interface ActionButtonProps extends ActionIconProps {
  color?: ButtonColorVariant;
  dataTestId?: string;
}

export const Button = (props: ButtonProps & ActionButtonProps) => {
  const {
    children,
    iconOnly,
    size,
    variant = 'filled',
    dataTestId = 'button',
    color = 'primary',
    ...rest
  } = props;

  if (iconOnly) {
    return (
      <ActionIcon
        {...rest}
        size={size}
        variant={variant}
        data-testid={dataTestId}
      >
        {children}
      </ActionIcon>
    );
  }

  return (
    <MButton
      {...rest}
      size={size}
      variant={variant}
      data-testid={dataTestId}
      classNames={classNames}
      data-type={color}
    >
      {children}
    </MButton>
  );
};
