.root {
  &[data-disabled] {
    opacity: 0.4;

    &:hover {
      box-shadow: none;
    }
  }

  &[data-type='primary'] {
    background-color: var(--mantine-color-yellow-5);

    &:hover {
      background-color: var(--mantine-color-yellow-6);
      box-shadow: 0 0 8px 0 var(--mantine-color-yellow-6);
    }
  }

  &[data-type='secondary'] {
    background-color: var(--mantine-color-light-blue-5);

    &:hover {
      background-color: var(--mantine-color-light-blue-6);
      box-shadow: 0 0 8px 0 var(--mantine-color-light-blue-6);
    }
  }

  &[data-type='transparent'] {
    background-color: 'transparent';

    &:hover {
      background-color: 'transparent';
      box-shadow: 'transparentF';
    }
  }
}

.label {
  line-height: 1.1;
}
