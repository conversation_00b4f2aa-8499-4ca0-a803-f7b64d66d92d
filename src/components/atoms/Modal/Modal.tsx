import { PropsWithChildren } from 'react';
import {
  Modal as MModal,
  ModalProps as MModalProps,
  ModalBaseStylesNames,
} from '@mantine/core';

import { useModalStore } from '@/apps/shop/stores/useModalStore';
import styles from './modal.module.css';

const MODAL_CLASSES = {
  body: styles.body,
};

export interface ModalProps extends Partial<MModalProps>, PropsWithChildren {
  name: string;
  customClasses?: Partial<Record<ModalBaseStylesNames, string>>;
}

export const Modal = ({
  children,
  name,
  customClasses = {},
  withCloseButton = false,
  onClose,
  ...rest
}: ModalProps) => {
  const { modalOption, closeModal } = useModalStore();

  const handleClose = () => {
    if (onClose) {
      onClose();

      return;
    }

    closeModal();
  };

  return (
    <MModal
      {...rest}
      opened={Boolean(modalOption.name) && modalOption.name.includes(name)}
      onClose={handleClose}
      centered
      withCloseButton={withCloseButton}
      classNames={{ ...MODAL_CLASSES, ...customClasses }}
    >
      {children}
    </MModal>
  );
};
