import React from 'react';
import { Menu as MMenu, MenuProps as MMenuProps } from '@mantine/core';

import style from './menu.module.css';

const CLASSES = {
  dropdown: style.dropdown,
};

export interface MenuOptionProps {
  label: string;
  onClick?: (event?: unknown) => void;
  color?: string;
}

interface MenuProps extends MMenuProps {
  options: MenuOptionProps[];
}

export const Menu = (props: MenuProps) => {
  const {
    children,
    options,
    trigger = 'hover',
    position = 'bottom-end',
    keepMounted = false,
    onClose,
  } = props;

  return (
    <MMenu
      trigger={trigger}
      onClose={onClose}
      offset={0}
      position={position}
      keepMounted={keepMounted}
      classNames={CLASSES}
    >
      <MMenu.Target>{children}</MMenu.Target>

      <MMenu.Dropdown>
        {options.map(({ label, onClick }) => {
          return (
            <React.Fragment key={label}>
              <MMenu.Item onClick={onClick}>{label}</MMenu.Item>
            </React.Fragment>
          );
        })}
      </MMenu.Dropdown>
    </MMenu>
  );
};
