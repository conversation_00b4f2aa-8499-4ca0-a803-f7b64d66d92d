import { MouseEvent } from 'react';
import { ActionIcon as MActionIcon, ActionIconProps } from '@mantine/core';

import { WithTooltipProps, withTooltip } from '../Tooltip';
import styles from './action-icon.module.css';

interface Props extends ActionIconProps {
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  dataTestId?: string;
  tooltip?: WithTooltipProps;
}

const classNames = {
  root: styles.root,
};

export const ActionIcon = ({
  children,
  dataTestId,
  size = 'lg',
  radius = 'xl',
  variant = 'transparent',
  onClick,
  tooltip,
  ...rest
}: Props) => {
  return withTooltip(
    <MActionIcon
      {...rest}
      size={size}
      radius={radius}
      variant={variant}
      data-testid={dataTestId}
      classNames={classNames}
      onClick={onClick}
    >
      {children}
    </MActionIcon>,
    tooltip,
  );
};
