import { ReactNode } from 'react';
import { Control, useController } from 'react-hook-form';
import { Checkbox, CheckboxProps, Group } from '@mantine/core';

import { DefaultItemOption } from '@/types/utility';

import { CheckboxFieldBase } from './Checkbox';
import styles from './checkbox.module.css';

type BaseCheckboxProps = CheckboxProps;

interface CheckboxFieldProps extends Omit<BaseCheckboxProps, 'onChange'> {
  name: string;
  label: string | ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  onChange?: (name: string, value: string[]) => void;
  options: DefaultItemOption[];
  groupClassName?: string;
}

const CLASSES = {
  label: styles.groupLabel,
};

export const CheckboxGroupField = (props: CheckboxFieldProps) => {
  const {
    name,
    label,
    control,
    description,
    disabled,
    onChange,
    size,
    options,
    groupClassName,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: [],
  });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { ref, ...rest } = field;

  const handleChange = (value: string[]) => {
    if (onChange) {
      onChange(name, value);

      return;
    }

    field.onChange(value);
  };

  return (
    <Checkbox.Group
      {...rest}
      label={label}
      description={description}
      onChange={handleChange}
      error={fieldState.error ? fieldState.error.message : null}
      classNames={CLASSES}
    >
      <Group className={groupClassName}>
        {options.map((option) => (
          <CheckboxFieldBase
            key={option.value}
            value={option.value}
            label={option.label}
            size={size}
            disabled={disabled}
          />
        ))}
      </Group>
    </Checkbox.Group>
  );
};
