import { ChangeEvent, LegacyRef, ReactNode } from 'react';
import { Control, useController } from 'react-hook-form';
import { Checkbox, CheckboxProps } from '@mantine/core';

import styles from './checkbox.module.css';

interface BaseCheckboxProps extends CheckboxProps {
  inputRef?: LegacyRef<HTMLInputElement>;
}

interface CheckboxFieldProps extends Omit<BaseCheckboxProps, 'onChange'> {
  name: string;
  label: string | ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  onChange?: (name: string, value: boolean) => void;
}

const CLASSES = {
  label: styles.label,
};

export const CheckboxField = (props: CheckboxFieldProps) => {
  const { name, label, control, description, disabled, onChange, size } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: false,
  });
  const { ref, ...rest } = field;

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(name, event.target.checked);

      return;
    }

    field.onChange(event.target.checked);
  };

  return (
    <CheckboxFieldBase
      {...rest}
      onChange={handleChange}
      inputRef={ref}
      label={label}
      checked={Boolean(field.value)}
      description={description}
      error={fieldState.error ? fieldState.error.message : null}
      disabled={disabled}
      size={size}
    />
  );
};

export function CheckboxFieldBase(props: BaseCheckboxProps) {
  const { inputRef, size = 'xs', ...rest } = props;

  return <Checkbox {...rest} ref={inputRef} classNames={CLASSES} size={size} />;
}
