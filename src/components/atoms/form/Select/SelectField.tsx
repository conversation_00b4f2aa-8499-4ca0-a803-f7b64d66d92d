import React, { LegacyRef } from 'react';
import { Control, useController } from 'react-hook-form';
import { Select as MSelect, SelectProps as MSelectProps } from '@mantine/core';

import { DefaultItemOption } from '@/types/utility';

import { INPUT_WRAPPER_ORDER } from '../Input/constant';
import inputStyles from '../Input/input.module.css';
import styles from './select.module.css';

export const CLASSES = {
  root: inputStyles.root,
  wrapper: inputStyles.wrapper,
  input: inputStyles.input,
  label: inputStyles.label,
  error: `${inputStyles.error} custom-error-field`,
  description: inputStyles.description,
  option: styles.option,
};
interface BaseSelectProps extends MSelectProps {
  inputRef?: LegacyRef<HTMLInputElement>;
  options: DefaultItemOption[];
  classes?: { [k: string]: string };
}

interface SelectFieldProps extends Omit<BaseSelectProps, 'onChange'> {
  name: string;
  label?: string | React.ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  placeholder?: string;
  description?: string;
  options: DefaultItemOption[];
  disabled?: boolean;
  onChange?: (
    name: string,
    value: string | null,
    option: DefaultItemOption,
  ) => void;
}

export const SelectField = (props: SelectFieldProps) => {
  const {
    name,
    label,
    control,
    options,
    placeholder = '',
    description,
    disabled,
    onChange,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: '',
  });

  const { ref, onChange: onFieldChange, ...rest } = field;

  const handleChange = (value: string | null, option: DefaultItemOption) => {
    if (onChange) {
      onChange(name, value, option);

      return;
    }

    onFieldChange(value);
  };

  return (
    <BaseSelectField
      {...rest}
      inputRef={ref}
      label={label}
      classNames={CLASSES}
      placeholder={placeholder}
      description={description}
      options={options}
      onChange={handleChange}
      disabled={disabled}
      error={fieldState.error ? fieldState.error.message : null}
    />
  );
};

export function BaseSelectField(props: BaseSelectProps) {
  const { inputRef, options, classes = {}, ...rest } = props;
  const classNames = { ...CLASSES, ...classes };

  return (
    <MSelect
      {...rest}
      ref={inputRef}
      data={options}
      classNames={classNames}
      inputWrapperOrder={INPUT_WRAPPER_ORDER}
      withCheckIcon={false}
      allowDeselect={false}
    />
  );
}
