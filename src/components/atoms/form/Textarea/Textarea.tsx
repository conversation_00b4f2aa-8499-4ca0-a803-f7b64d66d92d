import React, { ChangeEvent } from 'react';
import { Control, useController } from 'react-hook-form';
import { Textarea as MTextarea, TextareaProps } from '@mantine/core';

import { INPUT_WRAPPER_ORDER } from '../Input/constant';
import styles from '../Input/input.module.css';

type BaseTextareaProps = TextareaProps;

interface InputProps extends Omit<BaseTextareaProps, 'onBlur'> {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  onBlur?: (name: string, value: string) => void;
}

const CLASSES = {
  root: styles.root,
  wrapper: styles.wrapper,
  input: styles.input,
  label: styles.label,
  error: styles.error,
  description: styles.description,
};

export const TextareaField = (props: InputProps) => {
  const {
    name,
    label,
    control,
    placeholder = '',
    description,
    minRows,
    maxRows,
    autosize = true,
    size,
    onBlur,
    ...rest
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: '',
  });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { disabled, value, onChange, onBlur: onFormBlue, ...formRest } = field;

  const handleBlur = (event: ChangeEvent<HTMLTextAreaElement>) => {
    if (onBlur) {
      onBlur(name, event.target.value);
    }

    onFormBlue();
  };

  return (
    <BaseTextareaField
      {...rest}
      {...formRest}
      label={label}
      value={value ?? ''}
      onChange={onChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      disabled={disabled}
      description={description}
      error={fieldState.error ? fieldState.error.message : null}
      minRows={minRows}
      maxRows={maxRows}
      data-testid={name}
      size={size}
      autosize={autosize}
    />
  );
};

export function BaseTextareaField(props: BaseTextareaProps) {
  const { ...rest } = props;

  return (
    <MTextarea
      {...rest}
      inputWrapperOrder={INPUT_WRAPPER_ORDER}
      classNames={CLASSES}
    />
  );
}
