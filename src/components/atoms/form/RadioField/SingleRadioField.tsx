import { Radio, RadioProps } from '@mantine/core';
import { Control, useController } from 'react-hook-form';

import styles from './radio-field.module.css';

type BasicSingleRadioFieldProps = RadioProps;
interface SingleRadioFieldProps extends BasicSingleRadioFieldProps {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  defaultValue?: string;
}

const CLASSES = {
  root: styles.rootSingle,
  icon: styles.icon,
  radio: styles.radio,
};

export const SingleRadioField = (props: SingleRadioFieldProps) => {
  const { name, label, control, description, disabled, defaultValue, size } =
    props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: '',
  });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { ref, ...rest } = field;

  return (
    <BasicSingleRadioField
      {...rest}
      checked={field.value === defaultValue}
      value={defaultValue}
      label={label}
      description={description}
      disabled={disabled}
      error={fieldState.error ? fieldState.error.message : null}
      size={size}
    />
  );
};

export const BasicSingleRadioField = ({
  ...props
}: BasicSingleRadioFieldProps) => {
  return <Radio {...props} classNames={CLASSES} />;
};
