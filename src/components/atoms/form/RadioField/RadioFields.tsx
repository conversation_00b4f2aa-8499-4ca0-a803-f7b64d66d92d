import { Radio, RadioProps, Group, MantineSize } from '@mantine/core';
import { Control, useController } from 'react-hook-form';

import { RadioOption } from '@/types/utility';

import styles from './radio-field.module.css';

interface RadioFieldsProps extends Omit<RadioProps, 'size'> {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  options: RadioOption[];
  size?: MantineSize;
}

const CLASSES = {
  root: styles.root,
  icon: styles.icon,
  radio: styles.radio,
  label: styles.label,
  error: styles.error,
};

const GROUP_CLASSES = {
  label: styles.groupLabel,
};

export const RadioFields = (props: RadioFieldsProps) => {
  const { name, label, control, description, disabled, options, size } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: null,
  });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { value: fieldValue, ref, ...rest } = field;

  return (
    <Radio.Group
      {...rest}
      value={`${fieldValue}`}
      label={label}
      description={description}
      readOnly={disabled}
      error={fieldState.error ? fieldState.error.message : null}
      classNames={GROUP_CLASSES}
      size={size}
    >
      <Group gap="lg">
        {options.map((item) => {
          const value =
            typeof item.value === 'boolean' ? `${item.value}` : item.value;

          return (
            <Radio
              key={`${item.value}`}
              checked={`${fieldValue}` === value}
              value={value}
              label={item.label}
              classNames={CLASSES}
            />
          );
        })}
      </Group>
    </Radio.Group>
  );
};
