body {
  .rootSingle {
    width: max-content;
    margin: 0 auto;
  }

  .root {
    margin-top: 0.5rem;
  }

  .groupLabel {
    font-size: 1rem;
    color: var(--mantine-color-dark-8);
  }

  .label {
    font-size: 1rem;
    color: var(--mantine-color-dark-6);
    margin-bottom: 0.5rem;
  }

  .icon {
    transition: none;
  }

  .radio {
    background-color: white;
    border: 1px solid var(--mantine-color-dark-8);

    &:checked {
      border-color: var(--mantine-color-dark-8);
      background-color: white;

      & + .icon circle {
        fill: var(--mantine-color-light-blue-9);
      }
    }
  }
}
