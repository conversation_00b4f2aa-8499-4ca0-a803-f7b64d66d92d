import { ChangeEvent } from 'react';
import { Control, useController } from 'react-hook-form';
import { PasswordInput, TextInputProps } from '@mantine/core';

import styles from '@/components/atoms/form/Input/input.module.css';

interface InputPasswordProps extends TextInputProps {
  name: string;
  label: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  placeholder?: string;
}

const CLASSES = {
  root: styles.root,
  wrapper: styles.wrapper,
  input: styles.input,
  label: styles.label,
  error: styles.error,
  description: styles.description,
};

export const InputPasswordField = (props: InputPasswordProps) => {
  const {
    name,
    label,
    control,
    placeholder = '',
    onKeyDown,
    disabled,
    size,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: '',
  });

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    event.target.value = event.target.value.trim();

    field.onChange(event);
  };

  return (
    <PasswordInput
      {...field}
      label={label}
      classNames={CLASSES}
      placeholder={placeholder}
      onChange={handleChange}
      error={fieldState.error ? fieldState.error.message : null}
      data-testid={name}
      onKeyDown={onKeyDown}
      disabled={disabled}
      size={size}
    />
  );
};
