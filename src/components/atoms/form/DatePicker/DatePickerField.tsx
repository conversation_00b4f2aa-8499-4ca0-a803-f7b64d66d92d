import { Control, useController } from 'react-hook-form';
import { DateInput, DateInputProps } from '@mantine/dates';

import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import CalendarIcon from './assets/calendar.svg?react';
import { INPUT_WRAPPER_ORDER } from '../Input/constant';
import inputStyles from '../Input/input.module.css';
import styles from './DatePicker.module.css';

type BaseDatePickerProps = DateInputProps;

interface DatePickerFieldProps extends BaseDatePickerProps {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
}

const CLASSES = {
  wrapper: inputStyles.wrapper,
  input: `${inputStyles.input} ${styles.input}`,
  label: inputStyles.label,
  error: `${inputStyles.error} custom-error-field`,
  description: inputStyles.description,
  calendarHeader: styles.calendarHeader,
  day: styles.day,
  monthsListCell: styles.month,
  yearsListCell: styles.year,
  calendarHeaderControl: styles.controls,
  calendarHeaderLevel: styles.controlLevels,
};

function dateValidation(date: string): Date | null {
  const timestamp = Date.parse(date);

  if (isNaN(timestamp)) {
    return null;
  }

  return new Date(timestamp);
}

export const DatePickerField = (props: DatePickerFieldProps) => {
  const {
    name,
    label,
    control,
    placeholder = '',
    description,
    minDate,
    maxDate,
    clearable,
    size,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: null,
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { onChange, ref, ...rest } = field;

  const handleChange = (value: Date | null) => {
    onChange(value);
  };

  return (
    <BaseDatePicker
      {...rest}
      label={label}
      classNames={CLASSES}
      placeholder={placeholder}
      onChange={handleChange}
      error={fieldState.error ? fieldState.error.message : null}
      description={description}
      minDate={minDate}
      maxDate={maxDate}
      clearable={clearable}
      size={size}
      rightSection={<CalendarIcon />}
    />
  );
};

export function BaseDatePicker(props: BaseDatePickerProps) {
  return (
    <DateInput
      {...props}
      classNames={CLASSES}
      dateParser={dateValidation}
      valueFormat={DEFAULT_DISPLAY_DATE_FORMAT}
      inputWrapperOrder={INPUT_WRAPPER_ORDER}
    />
  );
}
