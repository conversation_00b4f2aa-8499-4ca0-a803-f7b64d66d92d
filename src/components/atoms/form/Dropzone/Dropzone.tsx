import { Control, useController } from 'react-hook-form';
import { Dropzone } from '@mantine/dropzone';
import { FileWithPath } from 'react-dropzone-esm';
import { Input, LoadingOverlay, Text } from '@mantine/core';
import clsx from 'clsx';

import AttachIcon from '@/assets/images/attach.svg?react';

import styles from './dropzone.module.css';

interface DropzoneFieldProps {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  label?: string;
  placeholder?: string;
  accept?: string[];
  isMultiple?: boolean;
  maxSize?: number;
  className?: string;
  onChange?: (data: { name: string; value: File }) => void;
  isLoading?: boolean;
}

const ACCEPT = ['image/png', 'image/jpeg', 'image/jpg'];

function getFileName(value: string | File): string {
  if (typeof value === 'string') {
    return value.split('/').pop() as string;
  }

  return value.name;
}

export const DropzoneField = (props: DropzoneFieldProps) => {
  const {
    name,
    label,
    control,
    placeholder = '',
    isMultiple = false,
    accept = ACCEPT,
    maxSize = 5242880, // 5Mb
    className,
    onChange,
    isLoading,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: null,
  });

  const handleDrop = (files: FileWithPath[]) => {
    const file = (isMultiple ? files : files[0]) as File;

    if (onChange) {
      onChange({ name, value: file });

      return;
    }

    field.onChange(file);
  };

  const fileName = field.value ? getFileName(field.value) : placeholder;

  return (
    <div className={clsx(styles.root, className)}>
      <LoadingOverlay visible={isLoading} />
      {label ? (
        <Text size="md" component="div" mb="1rem">
          {label}
        </Text>
      ) : null}
      <Dropzone
        onDrop={handleDrop}
        maxSize={maxSize}
        accept={accept}
        multiple={isMultiple}
        title={fileName}
        ref={field.ref}
      >
        <div className={styles.placeholder}>
          <AttachIcon />

          <Text size="md" c="dimmed" inline p={8} truncate="end" span>
            {fileName}
          </Text>
        </div>

        {fieldState.error ? (
          <div className={styles.error}>
            <Input.Error>{fieldState.error.message}</Input.Error>
          </div>
        ) : null}
      </Dropzone>
    </div>
  );
};
