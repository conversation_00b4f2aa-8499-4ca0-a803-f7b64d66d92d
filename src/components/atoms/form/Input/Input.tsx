import React, { ChangeEvent, LegacyRef } from 'react';
import { Control, useController } from 'react-hook-form';
import { TextInput, TextInputProps } from '@mantine/core';

import styles from './input.module.css';
import { INPUT_WRAPPER_ORDER } from './constant';

interface BaseInputProps extends TextInputProps {
  inputRef?: LegacyRef<HTMLInputElement>;
}

interface InputProps extends BaseInputProps {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
}

const CLASSES = {
  root: styles.root,
  wrapper: styles.wrapper,
  input: styles.input,
  label: styles.label,
  error: styles.error,
  description: styles.description,
};

export const InputField = (props: InputProps) => {
  const {
    name,
    label,
    control,
    placeholder = '',
    description,
    disabled,
    onChange,
    onKeyDown,
    maxLength,
    size,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: '',
  });
  const {
    disabled: disabledField,
    value,
    ref,
    onChange: onChangeField,
    ...rest
  } = field;

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(event);

      return;
    }

    onChangeField(event.target.value);
  };

  const isDisabled = disabledField || disabled;

  return (
    <BaseInputField
      {...rest}
      inputRef={ref}
      label={label}
      value={value ?? ''}
      placeholder={placeholder}
      description={description}
      onChange={handleChange}
      disabled={isDisabled}
      error={fieldState.error ? fieldState.error.message : null}
      maxLength={maxLength}
      data-testid={name}
      onKeyDown={onKeyDown}
      size={size}
    />
  );
};

export function BaseInputField(props: BaseInputProps) {
  const { inputRef, ...rest } = props;
  return (
    <TextInput
      {...rest}
      ref={inputRef}
      inputWrapperOrder={INPUT_WRAPPER_ORDER}
      classNames={CLASSES}
    />
  );
}
