import React, { LegacyRef } from 'react';
import { Control, useController } from 'react-hook-form';
import {
  NumberInput,
  NumberInputProps as MNumberInputProps,
} from '@mantine/core';

import { INPUT_WRAPPER_ORDER } from './constant';
import styles from './input.module.css';

interface BaseNumberInputProps extends MNumberInputProps {
  inputRef?: LegacyRef<HTMLInputElement>;
}

interface NumberInputProps extends Omit<BaseNumberInputProps, 'onChange'> {
  name: string;
  label?: string | React.ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  hideControls?: boolean;
  onChange?: (name: string, value: number | null | string) => void;
}

const CLASSES = {
  // root: styles.root,
  // wrapper: styles.wrapper,
  input: styles.input,
  label: styles.label,
  error: `${styles.error} custom-error-field`,
  description: styles.description,
};

export const NumberInputField = (props: NumberInputProps) => {
  const {
    name,
    label,
    control,
    hideControls = true,
    maxLength,
    placeholder = '',
    description,
    size,
    rightSection,
    leftSection,
    prefix,
    max,
    onChange,
  } = props;
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: null,
  });

  const { onChange: onFieldChange, value, ref, ...rest } = field;

  const handleChange = (value: number | string) => {
    const val = value !== '' ? Number(value) : null;

    if (onChange) {
      onChange(name, val);

      return;
    }

    onFieldChange(val);
  };

  return (
    <BaseNumberInput
      {...rest}
      inputRef={ref}
      value={value || null}
      label={label}
      placeholder={placeholder}
      description={description}
      onChange={handleChange}
      hideControls={hideControls}
      maxLength={maxLength}
      error={fieldState.error ? fieldState.error.message : null}
      size={size}
      rightSection={rightSection}
      leftSection={leftSection}
      prefix={prefix}
      max={max}
    />
  );
};

export function BaseNumberInput(props: BaseNumberInputProps) {
  const { inputRef, ...rest } = props;

  return (
    <NumberInput
      {...rest}
      ref={inputRef}
      classNames={CLASSES}
      inputWrapperOrder={INPUT_WRAPPER_ORDER}
    />
  );
}
