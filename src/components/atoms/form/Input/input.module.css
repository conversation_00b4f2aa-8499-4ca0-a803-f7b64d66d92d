body {
  .input {
    color: var(--mantine-color-dark-6);

    &:focus,
    &:focus-within {
      box-shadow: none !important;
      border-color: var(--mantine-color-dark-3);
    }

    &:has(input) {
      overflow: visible;
      border-radius: 0.5rem;
      /* for remove second shadow from password input components */
      box-shadow: none !important;

      & > input {
        &:focus,
        &:focus-within {
          box-shadow: none !important;
          border-color: var(--mantine-color-dark-4);
        }
      }
    }

    input {
      border-radius: 0.5rem;
      color: var(--mantine-color-dark-6);
    }
  }

  .label {
    font-size: 14px;
    line-height: 1;
    margin-bottom: 0.5rem;
    color: #344054;

    & > div {
      display: flex;
      column-gap: 0.5rem;
    }
  }

  .error {
    font-size: 0.85rem;
    margin-top: 0.25rem;
  }

  .description {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    display: flex;
  }
}
