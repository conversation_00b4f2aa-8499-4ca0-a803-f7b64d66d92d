import { PropsWithChildren, ReactNode } from 'react';
import {
  FloatingPosition,
  Tooltip as TooltipMantine,
  TooltipProps as MTooltipProps,
  TransitionOverride,
} from '@mantine/core';

import styles from './tooltip.module.css';

export interface TooltipProps extends MTooltipProps {
  label: string | ReactNode;
  tooltipWidthContent?: number | string;
  position?: FloatingPosition;
  contentClassName?: string;
  classes?: { [k: string]: string };
}

export interface WithTooltipProps extends PropsWithChildren {
  label: string | ReactNode;
  tooltipWidthContent?: number | string;
  position?: FloatingPosition;
  contentClassName?: string;
}

const TRANSITION: TransitionOverride = { transition: 'scale', duration: 300 };
const CLASSES = {
  tooltip: styles.tooltip,
};

export const Tooltip = (props: TooltipProps) => {
  const {
    children,
    label,
    position = 'bottom',
    tooltipWidthContent = 'auto',
    contentClassName,
    classes = {},
    ...rest
  } = props;

  if (!label) {
    return children;
  }

  const classNames = { ...CLASSES, ...classes };

  return (
    <TooltipMantine
      multiline
      w={tooltipWidthContent}
      label={label}
      position={position}
      transitionProps={TRANSITION}
      classNames={classNames}
      {...rest}
    >
      <div className={contentClassName ?? styles.contentContainer}>
        {children}
      </div>
    </TooltipMantine>
  );
};

export const withTooltip = (
  children: ReactNode,
  tooltip?: WithTooltipProps,
) => {
  const { label, ...rest } = tooltip || {};

  return label ? (
    <Tooltip label={label} {...rest}>
      {children}
    </Tooltip>
  ) : (
    children
  );
};
