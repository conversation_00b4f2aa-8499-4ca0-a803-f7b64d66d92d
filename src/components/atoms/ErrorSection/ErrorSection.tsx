import { ReactNode } from 'react';
import { Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';

import { Button } from '@/components';
import ErrorImg from '@/assets/images/error-dog.svg?react';

import styles from './error-section.module.css';

interface ErrorBaseSectionProps {
  title?: string;
  subtitle?: string;
  hasButton?: boolean;
  button?: ReactNode;
  classes?: string;
}

type ErrorSectionProps<T extends ErrorBaseSectionProps> = T extends {
  hasButton: true;
}
  ? T & { button: ReactNode }
  : T;

export const ErrorSection = (
  props: ErrorSectionProps<ErrorBaseSectionProps>,
) => {
  const { t } = useTranslation();
  const {
    title = t(`apiErrors.general`),
    subtitle = t(`apiErrors.generalHint`),
    button,
    hasButton = true,
    classes,
  } = props;

  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className={clsx(styles.container, classes)}>
      <ErrorImg />

      <Text component="h2" size="xl" mb="1rem" fw={700} c="light-blue.10">
        {title}
      </Text>

      <Text size="md" c="dark.8">
        {subtitle}
      </Text>

      {hasButton &&
        (button ? (
          button
        ) : (
          <Button onClick={handleReload} mt="1rem" size="sm" w={210}>
            {t('common.reload')}
          </Button>
        ))}
    </div>
  );
};
