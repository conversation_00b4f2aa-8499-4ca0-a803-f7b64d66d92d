import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface PortalProps {
  component: React.ReactNode;
  id: string;
}

export const Portal = (props: PortalProps) => {
  const { component, id } = props;

  const [isDomReady, setIsDomReady] = useState(false);

  useEffect(() => {
    setIsDomReady(true);
  }, []);

  if (!isDomReady) {
    return null;
  }

  return createPortal(component, document.getElementById(id)!);
};
