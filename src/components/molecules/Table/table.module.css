:root {
  --table-border-collapse: collapse;
}

body {
  .table {
    border-collapse: var(--table-border-collapse);
    border-spacing: 0 0.5rem;
    position: relative;
  }

  .thead {
    box-shadow: 0 0 5px 2px #0000000d;
    color: var(--mantine-color-dark-8);
    background-color: var(--mantine-color-light-blue-2);

    tr {
      th:nth-child(2) {
        padding-left: 0;
      }

      th:nth-last-child(2) {
        padding-right: 0;
      }
    }

    th {
      padding: 1rem;
      font-weight: 900;

      &:first-child {
        border-top-left-radius: 0.5rem;
      }

      &:last-child {
        border-top-right-radius: 0.5rem;
      }
    }
  }
}

.tbody {
  .tr {
    border-radius: 0.5rem;
    background-color: var(--mantine-color-white);
    transition: background-color 0.3s;
  }

  .td {
    padding: 0.5rem 1rem;
    color: var(--mantine-color-dark-6);
    font-size: 0.875rem;
    border-bottom: 1px solid var(--mantine-color-dark-3);
  }

  tr {
    td:nth-child(2) {
      padding-left: 0;
    }

    td:nth-last-child(2) {
      padding-right: 0;
    }
  }

  tr:last-child {
    td {
      border-bottom: none;
    }
  }
}

.loadingCell {
  height: 300px;
  text-align: center;
}

.emptySate {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.5rem;
  font-weight: 500;
}
