import { ReactNode } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  Row,
} from '@tanstack/react-table';
import { Loader, Table as MTable, Text } from '@mantine/core';
import clsx from 'clsx';

import { SortOptionsProps } from '@/types/utility';
import { Button } from '@/components';
import { SORT_ORDERS } from '@/constants';

import { getCellSize } from './utils';
import styles from './table.module.css';

export interface TableProps<T, K> {
  columns: ColumnDef<T, K>[];
  data: T[];
  infinityRef?: (node: Element) => void;
  classes?: string;
  isLoading?: boolean;
  emptyContent?: ReactNode;
  sortData?: {
    sortOptions: SortOptionsProps<T>;
    onSortChange: (sortOptions: SortOptionsProps<T>) => void;
    disabledColumnsSort?: string[];
  };
  onRowClick?: (row: Row<T>) => void;
  hasStickyHeader?: boolean;
  paddingCellWidth?: string | number;
  dataTestId?: string;
}

const CLASSES = {
  table: styles.table,
  thead: styles.thead,
  tbody: styles.tbody,
  tr: styles.tr,
  th: styles.th,
  td: styles.td,
};

const BORDER_STYLE = {
  border: 'none',
};

export const Table = <T, K>(props: TableProps<T, K>) => {
  const {
    columns,
    data,
    classes,
    isLoading,
    infinityRef,
    emptyContent,
    sortData,
    onRowClick,
    hasStickyHeader,
    paddingCellWidth = '2rem',
    dataTestId,
  } = props;

  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
  });
  const rows = table.getRowModel()?.rows;

  return (
    <MTable
      classNames={CLASSES}
      className={classes}
      stickyHeader={hasStickyHeader}
      withRowBorders={false}
      data-testid={dataTestId}
    >
      <MTable.Thead>
        {table.getHeaderGroups().map((headerGroup) => (
          <MTable.Tr key={headerGroup.id}>
            <MTable.Th
              miw={paddingCellWidth}
              w={paddingCellWidth}
              p="0"
              fs="0"
              style={BORDER_STYLE}
            />

            {headerGroup.headers.map((header) => {
              if (sortData) {
                const {
                  sortOptions,
                  onSortChange,
                  disabledColumnsSort = [],
                } = sortData;
                const isHideSort = disabledColumnsSort.includes(header.id);

                if (isHideSort) {
                  return (
                    <MTable.Th
                      key={header.id}
                      style={getCellSize(header.column.columnDef)}
                    >
                      <Text fw="700" c="dark.9">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                      </Text>
                    </MTable.Th>
                  );
                }

                const isSortBy = sortOptions.sortBy === header.id;

                const handleSort = () => {
                  onSortChange({
                    sortBy: header.id as keyof T,
                    sortOrder:
                      isSortBy && sortOptions.sortOrder === SORT_ORDERS.asc
                        ? SORT_ORDERS.desc
                        : SORT_ORDERS.asc,
                  });
                };

                return (
                  <MTable.Th
                    key={header.id}
                    style={getCellSize(header.column.columnDef)}
                  >
                    <div className="sortCell">
                      <Button
                        className="actionBtn"
                        onClick={handleSort}
                        color="transparent"
                        fw="700"
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                        {isHideSort ? null : (
                          <span
                            className={clsx('triangle', {
                              active: isSortBy,
                              up:
                                isSortBy &&
                                sortOptions.sortOrder === SORT_ORDERS.desc,
                            })}
                          />
                        )}
                      </Button>
                    </div>
                  </MTable.Th>
                );
              }

              return (
                <MTable.Th
                  key={header.id}
                  style={getCellSize(header.column.columnDef)}
                >
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext(),
                  )}
                </MTable.Th>
              );
            })}

            <MTable.Th
              miw={paddingCellWidth}
              w={paddingCellWidth}
              p="0"
              fs="0"
              style={BORDER_STYLE}
            />
          </MTable.Tr>
        ))}
      </MTable.Thead>

      {isLoading ? (
        <MTable.Tbody>
          <MTable.Tr>
            {/* colSpan = columns.length + 2 side empty columns */}
            <MTable.Td
              className={styles.loadingCell}
              colSpan={columns.length + 2}
            >
              <Loader size="3rem" />
            </MTable.Td>
          </MTable.Tr>
        </MTable.Tbody>
      ) : (
        <MTable.Tbody>
          {rows.length > 0 ? (
            rows.map((row, index, array) => {
              const handleRowClick = () => {
                if (onRowClick) {
                  onRowClick(row);
                }
              };

              return (
                <MTable.Tr
                  key={row.id}
                  ref={
                    index === array.length - 1
                      ? (infinityRef as () => void)
                      : null
                  }
                  onClick={handleRowClick}
                  className={clsx({ 'cursor-pointer': onRowClick })}
                >
                  <MTable.Td
                    miw={paddingCellWidth}
                    w={paddingCellWidth}
                    p="0"
                    fs="0"
                    style={BORDER_STYLE}
                  />

                  {row.getVisibleCells().map((cell) => {
                    const cellData = { ...cell.getContext() };

                    return (
                      <MTable.Td
                        key={cell.id}
                        style={getCellSize(cell.column.columnDef)}
                      >
                        {flexRender(cell.column.columnDef.cell, cellData)}
                      </MTable.Td>
                    );
                  })}

                  <MTable.Td
                    miw={paddingCellWidth}
                    w={paddingCellWidth}
                    p="0"
                    fs="0"
                    style={BORDER_STYLE}
                  />
                </MTable.Tr>
              );
            })
          ) : (
            <MTable.Tr>
              {/* colSpan = columns.length + 2 side empty columns */}
              <MTable.Td colSpan={columns.length + 2}>{emptyContent}</MTable.Td>
            </MTable.Tr>
          )}
        </MTable.Tbody>
      )}
    </MTable>
  );
};
