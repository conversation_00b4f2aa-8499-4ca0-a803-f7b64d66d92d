import { Alert } from '@mantine/core';

const EnvironmentBanner = () => {
  const env = import.meta.env.VITE_APP_ENV || 'production';
  if (env === 'production') return null;

  return (
    <Alert
      title={`Environment: ${env.toUpperCase()}`}
      color="var(--mantine-color-light-blue-9)"
      variant="filled"
      styles={{ title: { fontSize: '1rem', fontWeight: 600 } }}
    />
  );
};

export default EnvironmentBanner;
