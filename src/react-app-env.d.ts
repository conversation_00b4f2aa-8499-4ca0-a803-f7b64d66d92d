/// <reference types="vite/client" />
declare module '*.svg';
declare module '*.svg?react';
declare module '*.png';
declare module '*.jpg';
declare module '*.module.css';

interface ImportMetaEnv {
  readonly VITE_API_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare module '*.csv' {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  export default <{ [key: string]: any }>Array;
}

// declare const trResources: typeof import('./i18n/locales/en/translation.json');
// type TranslationKeys = typeof trResources;
// declare module 'react-i18next' {
//   // eslint-disable-next-line
//   interface Resources extends TranslationKeys {}
// }
