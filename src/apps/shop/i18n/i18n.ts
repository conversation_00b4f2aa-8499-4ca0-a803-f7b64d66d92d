import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { translationEN, translationKR } from './locales';

const resources = {
  en: {
    translation: translationEN,
  },
  kr: {
    translation: translationKR,
  },
};

i18n
  .use(initReactI18next)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    interpolation: {
      escapeValue: false,
    },
    defaultNS: 'translation',
  });

export default i18n;
