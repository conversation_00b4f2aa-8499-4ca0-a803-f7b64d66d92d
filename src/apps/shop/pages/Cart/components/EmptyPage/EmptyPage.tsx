import { Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components';

import styles from '../../Cart.module.css';

export const EmptyPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleGoToSearch = () => {
    navigate('/search');
  };

  return (
    <div className={styles.emptyPageRoot} data-testid="empty-page">
      <div className={styles.emptyPageContent}>
        <Text size="lgMd" fw="700">
          {t('client.cart.emptyCartTitle')}
        </Text>

        <Text>{t('client.cart.emptyCartDescription')}</Text>

        <Button onClick={handleGoToSearch} fullWidth>
          {t('common.search')}
        </Button>
      </div>
    </div>
  );
};
