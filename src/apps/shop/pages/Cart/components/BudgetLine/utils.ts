import { CSSProperties } from 'react';

export function getPercentage(value?: number, maxValue?: number) {
  if (!value || !maxValue) {
    return;
  }

  return (100 * value) / maxValue;
}

interface ProgressCSSProperties extends CSSProperties {
  '--bgColor': string;
  '--labelBgColor': string;
  '--shadowColor': string;
}

export function getProgressColor(progress?: number): ProgressCSSProperties {
  if (!progress) {
    return {
      '--bgColor': 'var(--mantine-color-dark-3)',
      '--labelBgColor': 'var(--mantine-color-dark-4)',
      '--shadowColor': ' 0 2px 4px 0 #00000026',
    };
  }

  if (progress < 75) {
    return {
      '--bgColor': 'var(--mantine-color-green-11)',
      '--labelBgColor': 'var(--mantine-color-green-1)',
      '--shadowColor': ' 0 2px 4px 0 #00000026',
    };
  }

  if (progress < 90) {
    return {
      '--bgColor': 'var(--mantine-color-yellow-4)',
      '--labelBgColor': 'var(--mantine-color-yellow-5)',
      '--shadowColor': ' 0 2px 4px 0 #00000026',
    };
  }

  if (progress <= 99) {
    return {
      '--bgColor': 'var(--mantine-color-red-2)',
      '--labelBgColor': 'var(--mantine-color-red-3)',
      '--shadowColor': ' 0 2px 4px 0 #00000026',
    };
  }

  return {
    '--bgColor': 'var(--mantine-color-red-2)',
    '--labelBgColor': 'var(--mantine-color-red-3)',
    '--shadowColor': '0px 0px 10px 0px var(--mantine-color-red-3)',
  };
}
