.box {
  margin-right: 1.5rem;
}

.topLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;

  svg {
    width: 0.75rem;
    height: 0.75rem;

    circle {
      fill: var(--mantine-color-dark-5);
    }

    path {
      fill: white;
    }
  }
}

.text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
}

body {
  .root {
    overflow: visible;
  }

  .section {
    overflow: visible;
    --progress-section-color: var(--bgColor) !important;
    position: relative;
    border-radius: 1rem;
  }
}
