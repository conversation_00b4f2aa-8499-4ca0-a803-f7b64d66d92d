import { useTranslation } from 'react-i18next';
import { Button, Text } from '@mantine/core';

import { MODAL_NAME } from '@/constants';
import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { Modal } from '@/components';
import BinIcon from '@/assets/images/cart/circled-bin.svg?react';
import { ProductType } from '@/types';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import styles from '../../Cart.module.css';

type RemoveModalOptions = ModalOptionProps & {
  product: ProductType;
  productOfferId: string;
};

export const RemoveItemModal = () => {
  const { t } = useTranslation();
  const { modalOption, closeModal } = useModalStore();
  const { addToCart } = useCartStore();
  const { product, productOfferId } = modalOption as RemoveModalOptions;

  if (!product || !productOfferId) {
    return null;
  }

  const handleRemove = () => {
    addToCart({
      offers: [
        {
          productOfferId,
          quantity: 0,
        },
      ],
      onError: () => {},
    });

    closeModal();
  };

  return (
    <Modal name={MODAL_NAME.REMOVE_PRODUCT_FROM_CART} size="md">
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <BinIcon />
          <Text fw="700" mb="1.5rem" mt="1rem" size="1.5rem">
            {t('client.cart.removeModalTitle')}
          </Text>
          <Text>{t('client.cart.removeModalText')}</Text>
          <Text fw="700" mb="2rem">
            {`${product?.name}?`.toUpperCase()}
          </Text>
        </div>
        <div className={styles.modalFooter}>
          <Button onClick={closeModal} variant="default" fullWidth>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleRemove} fullWidth>
            {t('common.remove')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
