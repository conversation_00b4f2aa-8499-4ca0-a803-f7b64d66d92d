import { useTranslation } from 'react-i18next';
import { Button, Text } from '@mantine/core';

import { MODAL_NAME } from '@/constants';
import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';

import { Modal } from '@/components';
import BinIcon from '@/assets/images/cart/circled-bin.svg?react';

import styles from '../../Cart.module.css';

type RemoveModalOptions = ModalOptionProps & {
  clearCart: () => Promise<void>;
};

export const ClearCartModal = () => {
  const { t } = useTranslation();
  const { modalOption, closeModal } = useModalStore();
  const { clearCart } = modalOption as RemoveModalOptions;

  if (!clearCart) {
    return null;
  }

  const handleClearCart = () => {
    clearCart();
    closeModal();
  };

  return (
    <Modal name={MODAL_NAME.CLEAR_CART} size="md">
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <BinIcon />
          <Text fw="700" mb="1.5rem" mt="1rem" size="1.5rem">
            {t('client.cart.clearCart')}
          </Text>
          <Text mb="2rem" ta="center">
            {t('client.cart.clearCartDescription')}
          </Text>
        </div>
        <div className={styles.modalFooter}>
          <Button onClick={closeModal} variant="default" fullWidth>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleClearCart} fullWidth>
            {t('common.clear')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
