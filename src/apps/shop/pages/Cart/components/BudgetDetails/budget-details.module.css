.container {
  background-color: var(--mantine-color-light-blue-2);
  border-radius: 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: #f2f8fc;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
}

body {
  .item {
    --item-filled-color: var(--mantine-color-light-blue-2);
  }

  .label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 0.5rem;

    & a {
      text-decoration: underline;
      margin: 0 0.5rem 0 0.25rem;
    }

    & > div {
      height: 18px;
    }
  }

  .control {
    &:hover {
      background-color: initial;
    }
  }

  .arrowBox {
    width: 22px;
    height: 22px;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--mantine-color-light-blue-5);

    & :global(.triangle) {
      border-width: 0 5px 8px 5px;
      border-color: transparent transparent var(--mantine-color-dark-8)
        transparent;
    }
  }
}

.content {
  display: grid;
  grid-template-columns: repeat(2, 1fr) 1px repeat(2, 1fr);
  grid-template-rows: repeat(2, max-content);
  grid-column-gap: 3rem;
  grid-row-gap: 0.5rem;
  padding: 0;
  position: relative;
  margin-top: 1rem;
  &:after {
    content: '';
    position: absolute;
    left: 50%;
    height: 100%;
    width: 1px;
    background: rgba(0, 0, 0, 0.05);
  }

  &:before {
    content: '';
    position: absolute;
    top: -1rem;
    height: 1px;
    width: 100%;
    background: rgba(0, 0, 0, 0.05);
  }
}
