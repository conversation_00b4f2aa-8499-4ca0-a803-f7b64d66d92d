.pageContentRoot {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 600px;

  .content {
    display: grid;
    grid-template-columns: minmax(100px, 1fr) 300px;
    gap: 1.25rem;
    margin-top: 2rem;
  }

  .loading {
    opacity: 0.5;
    pointer-events: none;
  }

  .vendorsList {
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .checkoutBoxContainer {
    display: flex;
  }
}

.emptyPageRoot {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;

  & > .emptyPageContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    max-width: 600px;
    text-align: center;

    a {
      color: var(--mantine-color-light-blue-8);
    }

    & > button:first-of-type {
      font-size: 1.125rem;
      margin-top: 0.5rem;
    }
  }
}

.modal {
  & > .modalContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--mantine-color-dark-8);
  }

  & > .modalFooter {
    display: flex;
    gap: 2.5rem;
    justify-content: space-between;
  }
}

@media (max-width: 1280px) {
  .pageContentRoot {
    .content {
      display: flex;
      flex-direction: column;
    }
  }

  .checkoutBoxContainer {
    justify-content: flex-end;
  }
}
