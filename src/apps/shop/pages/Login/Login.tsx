import { useNavigate } from 'react-router-dom';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import {
  type FormValues,
  LoginForm,
} from '@/libs/auth/components/LoginForm/LoginForm';
import { post } from '@/libs/utils/api';
import { UserType } from '@/types/common';

import { SCHEMA } from './constants';

const BASE_URL = import.meta.env.VITE_API_URL || 'https://127.0.0.1:8000';

export const Login = () => {
  const navigate = useNavigate();

  const loginApiFunc = async (values?: FormValues) => {
    if (!values) return;

    const { email, password } = values;

    const userData = await post<
      UserType & { clinicId: string | null; clinic_id: string | null }
    >({
      url: '/sessions',
      body: { email, password },
      withApi: false,
    });

    const queryParameters = new URLSearchParams(window.location.search);

    // TODO: Extract Amazon code from here
    const amazonCallbackUri = queryParameters.get('amazon_callback_uri');
    // TODO: This line should be changed in the future when the BE change the field name from clinic_id to clinicId
    const clinicId = userData.clinicId ?? userData.clinic_id;

    if (amazonCallbackUri && clinicId) {
      const amazonState = queryParameters.get('amazon_state');
      const amazonParams =
        '?amazon_state=' +
        amazonState +
        '&state=' +
        clinicId +
        '&redirect_uri=' +
        `${BASE_URL}/api/oauth2/amazon-business` +
        '&status=authentication_successful';

      window.location.href = amazonCallbackUri + amazonParams;

      return;
    }
  };

  return (
    <LoginForm
      apiFunc={loginApiFunc}
      onSuccess={() => navigate(SHOP_ROUTES_PATH.home)}
      forgotPasswordPath={SHOP_ROUTES_PATH.forgotPassword}
      schema={SCHEMA}
    />
  );
};
