import { ReactNode } from 'react';

interface OnboardingStepFormWrapperProps {
  children: ReactNode;
  onSubmit: VoidFunction;
}
export const OnboardingStepFormWrapper = ({
  children,
  onSubmit,
}: OnboardingStepFormWrapperProps) => {
  return (
    <div className="mt-8 w-full rounded-lg border border-solid border-[#d0d5dd59] p-6">
      <form onSubmit={onSubmit} id="stepForm">
        {children}
      </form>
    </div>
  );
};
