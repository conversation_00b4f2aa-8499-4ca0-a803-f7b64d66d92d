import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { OnboardingStep } from '../OnboardingStep/OnboardingStep';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Link, useNavigate } from 'react-router-dom';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Button } from '@/libs/ui/Button/Button';
import MailIcon from './assets/mail.svg?react';
import styles from './SuccessStep.module.css';

interface SuccessStepProps {
  onComplete: VoidFunction;
  gpoLogoUrl: string | null;
}
export const SuccessStep = ({ gpoLogoUrl }: SuccessStepProps) => {
  const navigate = useNavigate();
  return (
    <OnboardingStep
      title={
        <>
          Congratulations! <br /> Your Account Is Ready
        </>
      }
      subTitle="Explore exclusive offers, streamline your ordering, and access tools built to support your clinic's success."
      currentStep={3}
      totalSteps={3}
      gpoLogoUrl={gpoLogoUrl}
    >
      <div className="my-12 w-[200px]">
        <Logo type="emblem" />
      </div>
      <div className="mt-8 w-full">
        <Button
          type="submit"
          form="stepForm"
          onClick={() => {
            navigate(SHOP_ROUTES_PATH.vendors);
          }}
        >
          Set Up Your Vendors
        </Button>
      </div>

      <div className="mt-6">
        <Link to="/">
          <Text c="#333" size="16px">
            Go to Home Page
          </Text>
        </Link>
      </div>

      <Flex mt="2rem" p="md" bg="#F2F4F7" className={styles.infoBox} gap="md">
        <div>
          <MailIcon />
        </div>
        <div>
          <Text fw="500" size="16px" c="#222" lh="1.5">
            Heads up! Please check your email.
          </Text>
          <Text c="#555F74" size="14px" lh="1.5">
            We&apos;ve sent a welcome message with next steps and tips to get
            started. If it&apos;s not in your inbox, check your spam folder.
          </Text>
        </div>
      </Flex>
    </OnboardingStep>
  );
};
