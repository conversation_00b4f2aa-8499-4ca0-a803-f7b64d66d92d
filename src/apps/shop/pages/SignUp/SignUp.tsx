import { useEffect, useState } from 'react';
import { ClinicInformationStep } from './components/ClinicInformationStep/ClinicInformationStep';
import { CreatePasswordStep } from './components/CreatePasswordStep/CreatePasswordStep';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { UserType } from '@/types/common';
import { get } from '@/libs/utils/api';
import { SuccessStep } from './components/SuccessStep/SuccessStep';
import { useSearchParams } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';

export const SignUp = () => {
  const { getGpo } = useAuthStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [searchParams] = useSearchParams();
  let inviteEmail = '';

  const checkCurrentStep = async () => {
    setLoading(true);
    try {
      const userData = await get<UserType>({
        url: '/users/me',
        options: {
          suppressAuthRedirect: true,
        },
      });

      const hasClinics = userData.account.clinics.length > 0;
      if (hasClinics) {
        setCurrentStep(3);
      } else {
        setCurrentStep(2);
      }
    } catch {
      setCurrentStep(1);
    }
    setLoading(false);
  };

  useEffect(() => {
    checkCurrentStep();
  }, []);

  // TODO: Add loading state
  if (loading) {
    return null;
  }

  let gpoLogoUrl = null;

  const invitationToken = searchParams.get('invitationToken');

  if (invitationToken) {
    try {
      const { img, sub } = jwtDecode<{
        img: string;
        sub: string;
      }>(atob(invitationToken));

      gpoLogoUrl = img;
      inviteEmail = sub;
    } catch {
      console.error('Invalid invitation token');
    }
  }

  try {
    const gpo = getGpo();
    if (gpo) {
      gpoLogoUrl = gpo.imageUrl;
    }
  } catch {
    console.error('Erro on retrieve GPO information');
  }

  switch (currentStep) {
    case 1:
      return (
        <CreatePasswordStep
          onComplete={checkCurrentStep}
          gpoLogoUrl={gpoLogoUrl}
          inviteEmail={inviteEmail}
          invitationToken={invitationToken}
        />
      );
    case 2:
      return (
        <ClinicInformationStep
          onComplete={checkCurrentStep}
          gpoLogoUrl={gpoLogoUrl}
        />
      );
    case 3:
      return (
        <SuccessStep onComplete={checkCurrentStep} gpoLogoUrl={gpoLogoUrl} />
      );
  }
};
