import type { VendorType } from '@/types';
import { Badge } from '@/libs/ui/Badge/Badge';

interface VendorConnectionStatusProps {
  status: VendorType['status'];
}
export const VendorConnectionStatus = ({
  status,
}: VendorConnectionStatusProps) => {
  const statusConfigs: Record<VendorType['status'], string> = {
    connected: 'bg-[#89BF77] text-white',
    connecting: 'bg-[#B6F5F9] text-[#344054]',
    disconnected: '',
  };

  const style = statusConfigs[status];

  if (style) {
    return <Badge className={style}>{status}</Badge>;
  }

  return null;
};
