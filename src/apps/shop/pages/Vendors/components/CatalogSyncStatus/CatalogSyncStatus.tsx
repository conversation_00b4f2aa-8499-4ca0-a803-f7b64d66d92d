import { CatalogSyncStatusType } from '@/types';
import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { ReactNode } from 'react';
import RunningIcon from './assets/running.svg?react';
import SuccessIcon from './assets/success.svg?react';
import FailIcon from './assets/fail.svg?react';

interface CatalogSyncStatusProps {
  status: CatalogSyncStatusType;
}
const syncStatusConfigs: Record<
  CatalogSyncStatusType,
  {
    label: string;
    color: string;
    icon: ReactNode;
  }
> = {
  failed: {
    label: 'Sync Fail',
    color: '#F14336',
    icon: <FailIcon />,
  },
  pending: {
    label: 'Syncing Products',
    color: '#57ABB0',
    icon: <RunningIcon />,
  },
  running: {
    label: 'Syncing Products',
    color: '#57ABB0',
    icon: <RunningIcon />,
  },
  succeeded: {
    label: 'Synced',
    color: '#6AA555',
    icon: <SuccessIcon />,
  },
};

export const CatalogSyncStatus = ({ status }: CatalogSyncStatusProps) => {
  const { color, label, icon } = syncStatusConfigs[status];

  return (
    <Flex align="center" gap="0.25rem">
      {icon}
      <Text c={color} size="0.875rem" fw="500">
        {label}
      </Text>
    </Flex>
  );
};
