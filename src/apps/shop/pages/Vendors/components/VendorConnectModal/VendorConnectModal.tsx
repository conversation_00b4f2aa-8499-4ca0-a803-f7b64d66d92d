import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { Image, Text } from '@mantine/core';
import { Modal } from '@/components';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { AmazonVendorConnectModalForm } from './AmazonVendorConnectModalForm';
import { CommonVendorConnectModalForm } from './CommonVendorConnectModalForm';
import styles from './VendorConnectModal.module.css';
import { MODAL_NAME } from '@/constants';
import { CLINIC_UNIFIED_VENDOR_FORM_LINK } from './constants';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export const VendorConnectModal = () => {
  const { modalOption } = useModalStore();
  const { vendor } = modalOption as VendorConnectModalOptions;

  if (!vendor) {
    return null;
  }

  const isReconnect = Boolean(vendor.lastProductCatalogSync);
  const buttonLabel = isReconnect ? 'Update' : 'Connect';

  return (
    <Modal
      name={MODAL_NAME.VENDOR_CONNECT}
      size="md"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
    >
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <Image
            src={vendor.imageUrl}
            alt={vendor.name}
            fallbackSrc={defaultProductImgUrl}
            className={styles.vendorLogo}
            w="130px"
          />
          {vendor.name === 'Amazon' ? (
            <AmazonVendorConnectModalForm buttonLabel={buttonLabel} />
          ) : (
            <CommonVendorConnectModalForm buttonLabel={buttonLabel} />
          )}
          {!isReconnect && (
            <Text c="#222" size="14px" mt="24px">
              Don’t have an account with this vendor?{' '}
              <a
                href={CLINIC_UNIFIED_VENDOR_FORM_LINK}
                rel="noreferrer"
                target="_blank"
                style={{ textDecoration: 'none' }}
              >
                <Text c="#0072C6" fw="500" size="14px" span>
                  Create account.
                </Text>
              </a>
            </Text>
          )}
        </div>
      </div>
    </Modal>
  );
};
