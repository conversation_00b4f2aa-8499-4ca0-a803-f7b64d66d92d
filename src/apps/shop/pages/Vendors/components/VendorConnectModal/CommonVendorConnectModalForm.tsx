import { useForm } from 'react-hook-form';
import * as Yup from 'yup';
import i18n from '@/apps/shop/i18n';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';

import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { yupResolver } from '@hookform/resolvers/yup';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
// Box removed

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export interface VendorConnectForm {
  username: string;
  password: string;
}

const SCHEMA = Yup.object().shape({
  username: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.vendors.username'),
      }),
    ),
  password: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.vendors.password'),
      }),
    ),
});

interface CommonVendorConnectModalFormProps {
  buttonLabel: string;
}
export const CommonVendorConnectModalForm = ({
  buttonLabel,
}: CommonVendorConnectModalFormProps) => {
  const { modalOption, closeModal } = useModalStore();
  const { connectVendor } = useVendorsStore();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<VendorConnectForm>({
    resolver: yupResolver(SCHEMA),
  });
  const { vendor } = modalOption as VendorConnectModalOptions;

  const { apiRequest: handleConnect, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      await connectVendor({ vendorId: vendor.id, ...values });

      closeModal();
      reset();
    }),
  });

  if (!vendor) {
    return null;
  }

  return (
    <form onSubmit={handleConnect} className="w-full">
      <div className="mb-4">
        <Input
          label="Username"
          {...register('username')}
          error={errors.username?.message}
        />
      </div>
      <div className="mb-4">
        <Input
          label="Password"
          {...register('password')}
          error={errors.password?.message}
        />
      </div>
      <Button loading={isLoading}>{buttonLabel}</Button>
    </form>
  );
};
