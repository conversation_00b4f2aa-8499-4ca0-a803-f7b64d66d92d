import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { Image } from '@mantine/core';
import * as Yup from 'yup';
import i18n from '@/apps/shop/i18n';

import { MODAL_NAME } from '@/constants';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { Button, InputField, InputPasswordField, Modal } from '@/components';
import { VendorType } from '@/types';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { yupResolver } from '@hookform/resolvers/yup';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import styles from './VendorConnectModal.module.css';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export interface VendorConnectForm {
  username: string;
  password: string;
}

const SCHEMA = Yup.object().shape({
  username: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.vendors.username'),
      }),
    ),
  password: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.vendors.password'),
      }),
    ),
});

export const CommonVendorConnectModal = () => {
  const { t } = useTranslation();
  const { modalOption, closeModal } = useModalStore();
  const { connectVendor } = useVendorsStore();
  const { control, handleSubmit, reset } = useForm<VendorConnectForm>({
    resolver: yupResolver(SCHEMA),
  });
  const { vendor } = modalOption as VendorConnectModalOptions;

  const { apiRequest: handleConnect, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      await connectVendor({ vendorId: vendor.id, ...values });

      closeModal();
      reset();
    }),
  });

  if (!vendor) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.VENDOR_CONNECT}
      size="lg"
      customClasses={{
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
    >
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <InputField
            name="username"
            control={control}
            label={t('client.vendors.username')}
            disabled={isLoading}
          />

          <InputPasswordField
            name="password"
            control={control}
            label={t('client.vendors.password')}
            size="sm"
            disabled={isLoading}
          />
        </div>
        <div>
          <Button onClick={handleConnect} fullWidth loading={isLoading}>
            {t('common.connect')}
          </Button>
        </div>
        <Image
          src={vendor.imageUrl}
          alt={vendor.name}
          fallbackSrc={defaultProductImgUrl}
          className={styles.vendorLogo}
          w={220}
          h={140}
        />
      </div>
    </Modal>
  );
};
