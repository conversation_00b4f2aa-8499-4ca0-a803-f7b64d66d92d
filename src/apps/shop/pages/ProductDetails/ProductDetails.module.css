.innerContent {
  display: flex;
  flex-direction: column;
  row-gap: 1.5rem;
  margin-bottom: 2em;
}

.image {
  padding: 1rem;
  border: 2px solid var(--mantine-color-dark-4);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: max-content;
  position: relative;
  min-width: 300px;
  min-height: 300px;
  max-width: 410px;
  max-height: 410px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #fff;

  .inCart {
    position: absolute;
    left: 0.5rem;
    bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 2rem;
    column-gap: 0.725rem;
    color: var(--mantine-color-dark-8);
    font-weight: 500;
    border: 2px dashed var(--mantine-color-dark-8);
    border-radius: 0.5rem;

    svg > g {
      opacity: 1;
    }
  }
}

.infoRow {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.lastOrdered {
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
}

.productDetailList {
  margin-bottom: 1.5rem;
  li {
    list-style-type: '• ';
    margin-bottom: 0.625rem;

    &:last-child {
      margin-bottom: 0;
    }

    &::marker {
      color: #858585;
    }
  }
}

.productDetailListTitle {
  color: #858585;
  font-weight: 700;
}

.description {
  border: 2px solid rgba(0, 0, 0, 0.05);
  border-radius: 1rem;
}

@media (min-width: 1100px) {
  .innerContent {
    flex-direction: row;
    column-gap: 1.75rem;
  }

  .image {
    min-width: 410px;
    min-height: 410px;
  }
}
