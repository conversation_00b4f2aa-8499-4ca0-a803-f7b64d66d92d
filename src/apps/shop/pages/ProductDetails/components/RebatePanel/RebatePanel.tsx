import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import { get } from '@/libs/utils/api';
import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useEffect, useState } from 'react';

interface Rebate {
  description: string;
  id: string;
  name: string;
  type: string;
}

interface RebatePanelProps {
  productOfferId?: string;
}
export const RebatePanel = ({ productOfferId }: RebatePanelProps) => {
  const [rebates, setRebates] = useState<Rebate[]>([]);

  // TODO: Handle loading and error
  const { apiRequest } = useAsyncRequest({
    apiFunc: async () => {
      const response = await get<Rebate[]>({
        url: `/promotions?filter[type]=rebate&filter[product_offer_id]=${productOfferId}`,
      });

      return setRebates(response);
    },
  });

  useEffect(() => {
    apiRequest();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return rebates.length > 0 ? (
    <div className="mb-8">
      <CollapsiblePanel
        header={
          <Flex h="100%" align="center" ml="1.5rem">
            <Text size="md" fw={500}>
              Rebates
            </Text>
          </Flex>
        }
        content={
          <div className="mb-6 p-4">
            {rebates.map(({ id, name, description }, index) => (
              <div key={id}>
                {index !== 0 ? <Divider my="md" /> : null}
                <Text fw="500" mb="sm">
                  {name}
                </Text>
                <MarkdownRenderer markdown={description} />
              </div>
            ))}
          </div>
        }
        startOpen
      />
    </div>
  ) : null;
};
