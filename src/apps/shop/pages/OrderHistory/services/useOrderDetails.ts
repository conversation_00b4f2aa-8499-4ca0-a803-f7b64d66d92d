import type { OrderHistoryDetailsType } from '@/libs/orders/types';
import { useCallback, useEffect, useState } from 'react';
import { get } from '@/libs/utils/api';

interface useOrderDetailsProps {
  id: string;
}

export const useOrderDetails = ({ id }: useOrderDetailsProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [errorOnLoading, setErrorOnLoading] = useState(false);
  const [order, setOrder] = useState<OrderHistoryDetailsType | null>(null);

  const fetchOrderDetails = useCallback(async () => {
    setIsLoading(true);
    setErrorOnLoading(false);

    const response = await get<OrderHistoryDetailsType>({
      url: `/orders/${id}`,
    });

    setOrder(response);
  }, [id, setErrorOnLoading]);

  useEffect(() => {
    try {
      fetchOrderDetails();
    } catch {
      setErrorOnLoading(true);
    }

    setIsLoading(false);
  }, [fetchOrderDetails, setErrorOnLoading]);

  return {
    order,
    errorOnLoading,
    isLoading,
  };
};
