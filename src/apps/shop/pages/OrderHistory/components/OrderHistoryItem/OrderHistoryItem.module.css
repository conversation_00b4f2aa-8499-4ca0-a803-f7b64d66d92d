.container {
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.15);
  border-left: none;
  text-decoration: none;
}

.container:first-child,
.active + .container {
  border-top-color: rgba(0, 0, 0, 0.02);
}

.active {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-left: none;
  position: relative;
}

.active:before {
  content: '';
  width: 0.42rem;
  height: 100%;
  left: 0;
  background-color: #2857aa;
  position: absolute;
}
