import { ShippingStatusType } from '@/libs/orders/types';
import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { ORDER_STATUS_CONFIGS } from './constants';
import styles from './OrderStatus.module.css';

interface StepProps {
  completed: boolean;
  hasTail: boolean;
  completedColor: string;
}

const Step = ({ completed, hasTail, completedColor }: StepProps) => {
  const color = completed ? completedColor : '#D2D2D2';

  return (
    <>
      {hasTail && (
        <div className="h-0.5 w-3" style={{ backgroundColor: color }} />
      )}
      <div
        className="h-2 w-2 rounded-full"
        style={{ backgroundColor: color }}
      />
    </>
  );
};

interface OrderStatusProps {
  status: ShippingStatusType;
  align?: 'left' | 'center' | 'right';
  showStepProgress?: boolean;
}

export const OrderStatus = ({
  status,
  align = 'left',
  showStepProgress = false,
}: OrderStatusProps) => {
  const statusConfig = ORDER_STATUS_CONFIGS[status];

  if (!statusConfig) {
    return null;
  }

  const { color, Icon, label, step } = statusConfig;

  const statusSteps = Array(5)
    .fill(false)
    .map((_, index) => (
      <Step
        key={+index}
        completedColor={color}
        hasTail={index > 0}
        completed={Boolean(step && index < step)}
      />
    ));

  return (
    <Flex direction="column">
      <Flex align="center" gap=".375rem" justify={align}>
        {Icon ? <Icon className={styles.icon} /> : null}
        <Text c={color} size="xs" fw="bold">
          {label}
        </Text>
      </Flex>
      {showStepProgress && step && (
        <Flex mt="0.25rem" align="center" justify={align}>
          {statusSteps}
        </Flex>
      )}
    </Flex>
  );
};
