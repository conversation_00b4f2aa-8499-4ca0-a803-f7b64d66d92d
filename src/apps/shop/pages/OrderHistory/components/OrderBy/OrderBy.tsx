import { useState } from 'react';
import { Popover, Button, UnstyledButton } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import ArrowUpArrowDownIcon from '@/assets/images/product/arrow-down-arrow-up.svg?react';
import styles from './OrderBy.module.css';

interface OrderByProps {
  setSort: (sort: string) => void;
}

export const OrderBy = ({ setSort }: OrderByProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSortClick = (sort: string) => {
    setIsOpen(false);
    setSort(sort);
  };

  return (
    <Popover position="bottom" shadow="md" opened={isOpen} onChange={setIsOpen}>
      <Popover.Target>
        <Button
          leftSection={<ArrowUpArrowDownIcon />}
          variant="default"
          onClick={() => setIsOpen(!isOpen)}
        >
          Order by
        </Button>
      </Popover.Target>
      <Popover.Dropdown p="0.1rem" className={styles.dropdown}>
        <Flex direction="column">
          <UnstyledButton
            onClick={() => handleSortClick('orderDate')}
            px="1rem"
            py="0.3rem"
            className={styles.button}
          >
            Olders first
          </UnstyledButton>
          <UnstyledButton
            onClick={() => handleSortClick('-orderDate')}
            px="1rem"
            py="0.3rem"
            className={styles.button}
          >
            Newers first
          </UnstyledButton>
          <UnstyledButton
            onClick={() => handleSortClick('total')}
            px="1rem"
            py="0.3rem"
            className={styles.button}
          >
            Higher Total first
          </UnstyledButton>
          <UnstyledButton
            onClick={() => handleSortClick('-total')}
            px="1rem"
            py="0.3rem"
            className={styles.button}
          >
            Lower Total first
          </UnstyledButton>
        </Flex>
      </Popover.Dropdown>
    </Popover>
  );
};
