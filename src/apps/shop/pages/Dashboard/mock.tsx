import { VendorTableType } from './types';

const QUARTER_LABELS = ['Q1', 'Q2', 'Q3', 'Q4'];
const YEAR_LABELS = ['2023', '2024'];
// TODO generation func
export const QUARTER_DATA = {
  labels: QUARTER_LABELS,
  datasets: [
    {
      type: 'bar' as const,
      label: '2023',
      data: [2, 2.3, 3, 3.7],
      borderColor: '#FADF82',
      backgroundColor: '#FADF82',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 10,
    },
    {
      type: 'bar' as const,
      label: '2024',
      data: [2.2, 2.7, 3.3, 4],
      borderColor: '#CAE3F3',
      backgroundColor: '#CAE3F3',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 10,
    },
    {
      type: 'line' as const,
      label: 'Average',
      borderWidth: 2,
      borderColor: '#F4E1EB',
      data: [0.7, 1, 1.7, 2.5],
      datalabels: {
        display: false,
      },
      pointStyle: 'line',
    },
  ],
};
export const QUARTER_SECOND_DATA = {
  labels: QUARTER_LABELS,
  datasets: [
    {
      type: 'bar' as const,
      label: 'COGS',
      data: [3.1, 2.7, 2.1, 1.8],
      borderColor: '#FADF82',
      backgroundColor: '#FADF82',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 10,
    },
    {
      type: 'bar' as const,
      label: 'Revenue',
      data: [3.4, 3, 2.3, 1.9],
      borderColor: '#CAE3F3',
      backgroundColor: '#CAE3F3',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 10,
    },
    {
      type: 'line' as const,
      label: 'Average',
      borderWidth: 2,
      borderColor: '#F4E1EB',
      data: [2.3, 2.1, 1.1, 0.9],
      datalabels: {
        display: false,
      },
      pointStyle: 'line',
    },
  ],
};
export const OVERVIEW_DATA = {
  labels: YEAR_LABELS,
  datasets: [
    {
      type: 'bar' as const,
      label: 'Budget',
      data: [3, 2],
      borderColor: '#FADF82',
      backgroundColor: '#FADF82',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 20,
    },
    {
      type: 'bar' as const,
      label: 'COGS',
      data: [3.2, 2.4],
      borderColor: '#CAE3F3',
      backgroundColor: '#CAE3F3',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 20,
    },
    {
      type: 'bar' as const,
      label: 'Revenue',
      data: [9.5, 8.5],
      borderColor: '#F1D9E6',
      backgroundColor: '#F1D9E6',
      borderWidth: 1,
      barThickness: 30,
      maxBarThickness: 20,
    },
  ],
};

export const CARD_POINTS_GOOD = [
  'Pet Wellness Center',
  'Furry Friends Vet. Clinic',
  'Pet Haven Veterinary Clinic',
  'Gentle Paws Animal Clinic',
  'Happy Tails Animal Hospital',
];
export const CARD_POINTS_BAD = [
  'Best Friends Veterinary Hospital',
  'Paws & Claws Veterinary Clinic',
  'Healthy Pets Veterinary Center',
  'Whiskers and Wags Veterinary',
  'CarePaws Veterinary Clinic',
];

export const MOCK_TABLE_DATA: VendorTableType[] = [
  {
    id: '1',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/firstvet-md.png',
      name: 'First Vet',
    },
    spend: '500,000',
    orders: 1024,
    allOrders: 23,
    reqSpend: 70,
    rank: 2,
  },
  {
    id: '2',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/mwi-md.png',
      name: 'MWI',
    },
    spend: '242,214',
    orders: 850,
    allOrders: 12,
    reqSpend: 92,
    rank: 3,
  },
  {
    id: '3',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/hills-md.png',
      name: "Hill's",
    },
    spend: '634,032',
    orders: 1450,
    allOrders: 26,
    reqSpend: 84,
    rank: 1,
  },
  {
    id: '4',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/pharmasource-md.png',
      name: 'Pharmasource',
    },
    spend: '124,034',
    orders: 214,
    allOrders: 8,
    reqSpend: 12,
    rank: 6,
  },
  {
    id: '5',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/covetrus-md.png',
      name: 'Covetrus',
    },
    spend: '12,352',
    orders: 51,
    allOrders: 2,
    reqSpend: 20,
    rank: 5,
  },
  {
    id: '6',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/midwest-md.png',
      name: 'Midwest',
    },
    spend: '75,235',
    orders: 109,
    allOrders: 4,
    reqSpend: 94,
    rank: 4,
  },
  {
    id: '7',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/amazon-md.png',
      name: 'Amazon',
    },
    spend: '234,228',
    orders: 244,
    allOrders: 6,
    reqSpend: 8,
    rank: 10,
  },
  {
    id: '8',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/petscript-md.png',
      name: 'Petscript',
    },
    spend: '124,653',
    orders: 124,
    allOrders: 6,
    reqSpend: 23,
    rank: 8,
  },
  {
    id: '9',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/penn-md.png',
      name: 'Penn',
    },
    spend: '412,514',
    orders: 412,
    allOrders: 12,
    reqSpend: 24,
    rank: 9,
  },
  {
    id: '10',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/royal-canin-md.png',
      name: 'Royal Canin',
    },
    spend: '124,125',
    orders: 124,
    allOrders: 9,
    reqSpend: 85,
    rank: 14,
  },
  {
    id: '11',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/wedgewood-md.png',
      name: 'Wedgewood',
    },
    spend: '96,304',
    orders: 15,
    allOrders: 1,
    reqSpend: 28,
    rank: 13,
  },
  {
    id: '12',
    vendor: {
      imgUrl:
        'https://nyc3.digitaloceanspaces.com/highfive-vet-statics-fubrt/staging/vendors/new-england-md.png',
      name: 'New England',
    },
    spend: '41,304',
    orders: 121,
    allOrders: 9,
    reqSpend: 9,
    rank: 12,
  },
];
