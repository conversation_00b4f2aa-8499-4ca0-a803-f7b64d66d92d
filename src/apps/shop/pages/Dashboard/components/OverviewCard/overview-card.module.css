.item {
  border: 1px solid var(--mantine-color-dark-3);
  background-color: white;
  border-radius: 0.5rem;
  max-width: 30%;
  min-width: 325px;
  width: 100%;
  padding: 1rem 1rem 0.5rem;
  display: grid;
  grid-template-columns: minmax(0, 1fr) max-content;
  grid-template-rows: repeat(3, auto);
  grid-column-gap: 1.5rem;

  .title {
    grid-area: 1 / 1 / 2 / 2;
    line-height: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .value {
    grid-area: 2 / 1 / 3 / 2;
    line-height: 2.5rem;
  }

  .additionValue {
    grid-area: 3 / 1 / 4 / 2;
  }

  .icon {
    grid-area: 1 / 2 / 4 / 3;
  }

  svg {
    width: 72px;
    height: 72px;
  }
}
