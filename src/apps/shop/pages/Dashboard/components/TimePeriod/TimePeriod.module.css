.wrap {
  display: flex;
  align-items: flex-end;
  column-gap: 1rem;

  & > div:has(label) {
    width: 130px;
  }
}

body .timeSelect {
  position: relative;
  display: flex;
  flex-direction: column;

  & > span {
    display: inline-block;
    font-weight: 500;
    word-break: break-word;
    font-size: 0.75rem;
    line-height: 1rem;
    color: var(--mantine-color-dark-5);
    margin-bottom: 0.25rem;
  }

  :global(.triangle) {
    margin-left: 0.375rem;
  }

  & > button {
    letter-spacing: -0.5px;
    width: 170px;
  }
}
