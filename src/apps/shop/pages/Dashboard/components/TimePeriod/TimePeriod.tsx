import React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

import i18n from '@/apps/shop/i18n';
import { Select } from '@/libs/form/Select';

import styles from './TimePeriod.module.css';

const TODAY_DAYJS = dayjs(new Date());

const OPTIONS = [
  {
    label: i18n.t('client.dashboard.timePeriod.monthDate'),
    value: TODAY_DAYJS.format('MMMM').toUpperCase(),
  },
  {
    label: i18n.t('client.dashboard.timePeriod.lastMonth'),
    value: TODAY_DAYJS.subtract(1, 'month').format('YYYY-MMMM').toUpperCase(),
  },
  {
    label: i18n.t('client.dashboard.timePeriod.last3Month'),
    value: TODAY_DAYJS.subtract(3, 'month').format('YYYY-MMMM').toUpperCase(),
  },
  {
    label: i18n.t('client.dashboard.timePeriod.last6Month'),
    value: TODAY_DAYJS.subtract(6, 'month').format('YYYY-MMMM').toUpperCase(),
  },
  {
    label: i18n.t('client.dashboard.timePeriod.yearDate'),
    value: TODAY_DAYJS.subtract(1, 'year').format('YYYY-MMMM').toUpperCase(),
  },
];

interface TimePeriodType {
  getData: (value?: string) => void;
  timePeriod: string;
}

export const TimePeriod = (props: TimePeriodType) => {
  const { t } = useTranslation();

  return (
    <div className={styles.wrap}>
      <div className={styles.timeSelect}>
        <Select
          label={t('client.dashboard.timePeriod.label')}
          options={OPTIONS}
          onChange={(value) => props.getData(value.target.value)}
          variant="sm"
          name="timePeriod"
          value={props.timePeriod || ''}
        />
      </div>
    </div>
  );
};
