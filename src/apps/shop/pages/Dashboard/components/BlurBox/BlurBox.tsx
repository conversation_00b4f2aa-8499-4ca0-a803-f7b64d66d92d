import { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import { Text } from '@mantine/core';

import styles from './blur-box.module.css';

type BlurBox = PropsWithChildren;

export const BlurBox = ({ children }: BlurBox) => {
  const { t } = useTranslation();

  return (
    <div className={styles.root}>
      <Text span>{t('client.dashboard.comingSoon')}</Text>
      <div className={styles.blur}>{children}</div>
    </div>
  );
};
