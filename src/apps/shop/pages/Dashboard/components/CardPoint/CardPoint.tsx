import { ReactNode } from 'react';
import { Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components';

import { BlurBox } from '../BlurBox/BlurBox';
import styles from './card-point.module.css';

interface CardPointProps {
  title: string;
  icon: ReactNode;
  points: string[];
  color: string;
}

export const CardPoint = (props: CardPointProps) => {
  const { title, points, icon, color } = props;

  const { t } = useTranslation();

  return (
    <BlurBox>
      <div className={styles.wrap}>
        <Text size="lgMd" fw={700} mb="1.5rem">
          {title}
        </Text>

        {icon}

        <ul className={styles.list}>
          {points.map((item, index) => (
            <li key={index}>
              <span style={{ backgroundColor: color }}>{index + 1}</span>

              <Text>{item}</Text>
            </li>
          ))}
        </ul>

        <Button fullWidth>{t('client.dashboard.viewAll')}</Button>
      </div>
    </BlurBox>
  );
};
