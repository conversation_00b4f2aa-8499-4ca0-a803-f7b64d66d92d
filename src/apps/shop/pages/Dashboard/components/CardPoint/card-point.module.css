.wrap {
  max-width: calc(50% - 2rem);
  min-width: 500px;
  width: 100%;
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 0 7px 3px #0000000d;
  position: relative;

  & > svg {
    position: absolute;
    right: 2rem;
    top: 2rem;
  }
}

.list {
  margin-bottom: 1.5rem;
  list-style: none;
  padding-left: 2rem;

  li {
    position: relative;

    &:not(:last-child) {
      margin-bottom: 0.5rem;
    }

    & > span {
      position: absolute;
      left: -2rem;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 50%;
      height: 18px;
      width: 18px;
      font-weight: 700;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
