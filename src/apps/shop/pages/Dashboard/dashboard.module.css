.mainSection {
  & > div:first-child {
    align-items: flex-end;
  }

  .pageContentRoot {
    padding: 1.5rem;
    max-width: var(--container-max-width);
    margin: 0 auto;
  }

  table {
    max-width: calc(100% - 1rem);

    thead {
      background-color: white;
    }
  }
}

.header {
  display: flex;
  column-gap: 1rem;
  align-items: flex-end;

  label {
    font-size: 0.75rem;
    line-height: 1rem;
    color: var(--mantine-color-dark-5);
    margin-bottom: 0.25rem;
  }

  button {
    width: 160px;
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem 3rem;
  margin-bottom: 1.5rem;
  position: relative;
  min-height: 235px;
}

.vendorCell {
  display: flex;
  align-items: center;

  img {
    width: 65px;
  }
}
