import { ReactNode } from 'react';

export interface OverviewType {
  title: string;
  value: string;
  additionValue?: string;
  customIcon?: ReactNode;
}

export interface VendorTableType {
  id: string;
  vendor: {
    imgUrl: string;
    name: string;
  };
  spend: string;
  orders: number;
  allOrders: number;
  reqSpend: number;
  rank: number;
}

export interface OverViewType {
  revenue: number;
  cogs: number;
  ga: number;
  cogsToRevenuePercentage: number;
  gaToRevenuePercentage: number;
}
