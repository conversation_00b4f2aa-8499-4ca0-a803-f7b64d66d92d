import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button, ErrorSection } from '@/components';

import styles from './Page404.module.css';

export const Page404 = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleNavigate = () => {
    navigate(-1);
  };

  return (
    <ErrorSection
      title={t('client.page404.title')}
      subtitle={t('client.page404.subtitle')}
      classes={styles.container}
      button={
        <Button onClick={handleNavigate} w={210} mt="1.5rem">
          {t('client.page404.goBack')}
        </Button>
      }
    />
  );
};
