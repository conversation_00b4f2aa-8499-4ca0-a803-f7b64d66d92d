import { useNavigate } from 'react-router-dom';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { ForgotPasswordForm } from '@/libs/auth/components/ForgotPasswordForm/ForgotPasswordForm';
import { post } from '@/libs/utils/api';
import { successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

export const ForgotPassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const resetPasswordApiFunc = async (values?: { email: string }) => {
    if (!values) return;

    await post({
      url: '/password-resets',
      body: values,
    });

    successNotification(t('forgotPassword.resetMessage'));
  };

  return (
    <ForgotPasswordForm
      apiFunc={resetPasswordApiFunc}
      onSuccess={() => navigate(SHOP_ROUTES_PATH.login)}
      loginPath={SHOP_ROUTES_PATH.login}
      schema={SCHEMA}
    />
  );
};
