.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100vh;
  background-size: cover;
  background-image:
    linear-gradient(
      90deg,
      rgba(233, 205, 104, 0.15) 0%,
      rgba(189, 220, 240, 0.15) 50%,
      rgba(234, 197, 215, 0.15) 100%
    ),
    linear-gradient(
      90deg,
      var(--mantine-color-white) 50%,
      var(--mantine-color-light-blue-3) 100%
    );
}

.mainTitle {
  font-size: 3rem;
  font-weight: bold;
  color: var(--mantine-color-dark-8);
  margin-top: 3rem;
  text-align: center;
}

.secondaryTitle {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--mantine-color-dark-8);
  margin-bottom: 2rem;
  text-align: center;
}

.commonText {
  font-size: 1rem;
  color: var(--mantine-color-dark-6);
  margin-bottom: 2rem;
  text-align: center;
}

.connect {
  font-weight: bold;
  font-size: 1.5rem;
  color: var(--mantine-color-dark-8);
  margin-top: 3rem;
  text-align: center;
}

.content {
  min-height: 90vh;
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

.buttonGroup {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.buttonGroup > button {
  margin-top: 0.75rem;
  min-width: 325px;
}

.footer {
  height: 2rem;
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}

.footerText {
  font-size: 0.75rem;
  color: var(--mantine-color-dark-6);
  margin-bottom: 2rem;
  text-align: center;
}

@media (min-width: 768px) {
  .image {
    right: -24px;
  }
}
