import { useNavigate } from 'react-router-dom';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { ChangePasswordForm } from '@/libs/auth/components/ChangePasswordForm/ChangePasswordForm';
import { post } from '@/libs/utils/api';
import { successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

export const ChangePassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const changePasswordApiFunc = async (values?: {
    password: string;
    confirmPassword: string;
  }) => {
    if (!values) return;

    await post({
      url: '/password-resets/confirm',
      body: values,
    });

    successNotification(t('changePassword.successMessage'));
  };

  return (
    <ChangePasswordForm
      apiFunc={changePasswordApiFunc}
      onSuccess={() => navigate(SHOP_ROUTES_PATH.login)}
      schema={SCHEMA}
    />
  );
};
