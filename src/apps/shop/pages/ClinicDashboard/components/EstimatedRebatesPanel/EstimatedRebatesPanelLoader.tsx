import { Box } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import 'react-loading-skeleton/dist/skeleton.css';
import Skeleton from 'react-loading-skeleton';

export const EstimatedRebatesPanelLoader = () => {
  return (
    <CollapsiblePanel
      header={
        <Flex
          pl="1.5rem"
          pr="4rem"
          py="1rem"
          align="center"
          justify="space-between"
          w="100%"
        >
          <Flex align="center">
            <Box mr="md">
              <Skeleton height={24} width={150} />
            </Box>
            <Box>
              <Skeleton height={40} width={200} />
            </Box>
            <Box ml="md">
              <Skeleton height={20} width={20} circle />
            </Box>
          </Flex>
          <Flex align="center" miw="12rem">
            <Box mr="0.5rem">
              <Skeleton height={16} width={60} />
            </Box>
            <Skeleton height={32} width={80} />
          </Flex>
        </Flex>
      }
      content={
        <Flex direction="column" p="1.5rem">
          <Box mb="0.75rem">
            <Skeleton height={12} width={200} />
          </Box>
          <Flex direction="column" gap="md">
            {/* Vendor panels skeleton */}
            {[1, 2, 3].map((index) => (
              <Box
                key={index}
                p="1rem"
                style={{ border: '1px solid #e0e0e0', borderRadius: '8px' }}
              >
                <Flex align="center" justify="space-between" mb="1rem">
                  <Skeleton height={20} width={120} />
                  <Skeleton height={16} width={80} />
                </Flex>
                <Flex gap="md" mb="1rem">
                  <Box>
                    <Box mb="0.5rem">
                      <Skeleton height={14} width={100} />
                    </Box>
                    <Skeleton height={18} width={80} />
                  </Box>
                  <Box>
                    <Box mb="0.5rem">
                      <Skeleton height={14} width={100} />
                    </Box>
                    <Skeleton height={18} width={80} />
                  </Box>
                </Flex>
                <Skeleton height={8} width="100%" />
              </Box>
            ))}
          </Flex>
        </Flex>
      }
      startOpen
    />
  );
};
