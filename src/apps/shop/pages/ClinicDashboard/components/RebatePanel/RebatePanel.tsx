import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { RebateType } from '@/types/common';
import { getPriceString } from '@/utils';
import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { EstimateRebateProgress } from '../EstimateRebateProgress/EstimateRebateProgress';
import { getRemainingDays } from '../../utils/getRemainingDays';
import dayjs from 'dayjs';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { SuggestedOfferList } from '@/libs/products/components/SuggestedOfferList/SuggestedOffersList';
import { EstimateRebateCTA } from '../EstimateRebateCTA/EstimateRebateCTA';

interface RebatePanelProps {
  rebate: RebateType;
}
export const RebatePanel = ({ rebate }: RebatePanelProps) => {
  const remainingDays = getRemainingDays(rebate.promotion.endedAt);
  const startDate = dayjs(rebate.promotion.startedAt).format(
    DEFAULT_DISPLAY_DATE_FORMAT,
  );
  const endDate = dayjs(rebate.promotion.endedAt).format(
    DEFAULT_DISPLAY_DATE_FORMAT,
  );
  const hasSuggestedOffers = rebate.suggestedProductOffers.length > 0;
  const noMoneySpent = rebate.currentSpendAmount === '0.00';

  return (
    <CollapsiblePanel
      header={
        <Flex
          p="md"
          pr={hasSuggestedOffers ? '4rem' : 'md'}
          align="center"
          justify="space-between"
          w="100%"
        >
          <Flex align="center">
            <div>
              <Text
                c={remainingDays < 7 ? '#A31838' : '#ED7F02'}
                size="0.75rem"
                mb="0.5rem"
              >
                <Text fw="700" span>
                  {remainingDays}
                </Text>{' '}
                {remainingDays > 1 ? 'days ' : 'day '} remaining in rebate
                period
              </Text>
              <Text size="1rem" fw="700" mb="0.5rem" w="260px">
                {rebate.promotion.name}
              </Text>
              <Flex align="center" gap="0.25rem">
                <Text size="0.75rem" c="#666">
                  Start:{' '}
                  <Text c="#333" fw="700" span>
                    {startDate}
                  </Text>
                </Text>
                {' - '}
                <Text size="0.75rem" c="#666">
                  End:{' '}
                  <Text c="#333" fw="700" span>
                    {endDate}
                  </Text>
                </Text>
              </Flex>
            </div>
            <Divider orientation="vertical" mx="24px" />
            <div>
              <Text c="#344054" size="0.75rem" w="100px">
                Amount spent
              </Text>
              <Text c="#344054" fw="700" size="0.875rem" mt="0.5rem">
                {getPriceString(rebate.currentSpendAmount)}
              </Text>
            </div>
            <Divider orientation="vertical" mx="24px" />
            <div>
              <Text c="#344054" size="0.75rem" w="100px">
                Estimated rebate
              </Text>
              <Text c="#344054" fw="700" size="0.875rem" mt="0.5rem">
                {getPriceString(rebate.estimatedRebateAmount)}
              </Text>
            </div>
          </Flex>
          {noMoneySpent ? (
            <EstimateRebateCTA promotion={rebate.promotion} />
          ) : (
            <div className="ml-6">
              <EstimateRebateProgress {...rebate} />
            </div>
          )}
        </Flex>
      }
      content={
        hasSuggestedOffers ? (
          <div className="bg-white px-4 pb-4">
            <div className="bg-black/[0.02] p-4">
              <SuggestedOfferList offers={rebate.suggestedProductOffers} />
            </div>
          </div>
        ) : null
      }
      variant="clean"
    />
  );
};
