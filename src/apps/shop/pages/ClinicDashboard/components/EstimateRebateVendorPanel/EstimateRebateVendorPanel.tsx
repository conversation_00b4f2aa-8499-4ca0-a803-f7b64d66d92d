import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import { RebateType } from '@/types/common';
import { getPriceString } from '@/utils';
import { Image, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { RebateRemainingTime } from '../RebateRemainingTime/RebateRemainingTime';
import { EstimateRebateProgress } from '../EstimateRebateProgress/EstimateRebateProgress';
import { RebatePanel } from '../RebatePanel/RebatePanel';

interface EstimateRebateVendorPanelProps {
  rebates: RebateType[];
}
export const EstimateRebateVendorPanel = ({
  rebates,
}: EstimateRebateVendorPanelProps) => {
  const totalSpendAmount = rebates.reduce(
    (acc, rebate) => +rebate.currentSpendAmount + acc,
    0,
  );
  const {
    promotion,
    currentRebatePercent,
    nextTierRebatePercent,
    nextTierMinimumSpendAmountThreshold,
    currentSpendAmount,
  } = rebates[0];
  const { vendor, startedAt, endedAt } = promotion;
  const { name, imageUrl } = vendor;

  return (
    <CollapsiblePanel
      header={
        <Flex
          py="1rem"
          pl="1.5rem"
          pr="4rem"
          align="center"
          justify="space-between"
          w="100%"
        >
          <Flex align="center">
            <Image src={imageUrl} alt={name} h="2rem" />
            <Text ml="md" size="0.75rem" c="#344054">
              YTD Spend
            </Text>
            <Text size="1.2rem" fw="700" mx="0.5rem" c="#344054">
              {getPriceString(totalSpendAmount)}
            </Text>
            <HelpTooltip message="Total spend of eligible purchases with this specific supplier through Highfive." />

            {rebates.length === 1 && (
              <div className="ml-5">
                <RebateRemainingTime startDate={startedAt} endDate={endedAt} />
              </div>
            )}
          </Flex>
          {rebates.length === 1 && (
            <EstimateRebateProgress
              currentRebatePercent={currentRebatePercent}
              nextTierRebatePercent={nextTierRebatePercent}
              currentSpendAmount={currentSpendAmount}
              nextTierMinimumSpendAmountThreshold={
                nextTierMinimumSpendAmountThreshold
              }
            />
          )}
        </Flex>
      }
      content={
        <Flex p="1.5rem" direction="column" gap="md">
          {rebates.map((rebate) => (
            <RebatePanel key={rebate.id} rebate={rebate} />
          ))}
        </Flex>
      }
      startOpen
    />
  );
};
