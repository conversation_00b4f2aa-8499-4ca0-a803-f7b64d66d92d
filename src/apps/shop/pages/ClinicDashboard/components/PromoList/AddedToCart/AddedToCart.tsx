import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import styles from './AddedToCart.module.css';
import { Logo } from '@/libs/ui/Logo/Logo';
import {
  ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { getPriceString } from '@/utils';

type PromoOfferModalOptions = ModalOptionProps & {
  title: string;
  savings: number;
};

export const AddedToCart = () => {
  const { closeModal } = useModalStore();
  const { modalOption } = useModalStore();
  const { title, savings = 0 } = modalOption as PromoOfferModalOptions;

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_CONGRATS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex-col bg-white p-6 pt-0">
        <h3 className="mb-2 text-2xl font-medium">
          Congratulations! You got the deal!
        </h3>
        <p className="mb-6 text-sm text-black/65">
          Proceed to checkout now and lock in your savings before the deal
          expires!
        </p>
        <div className="mb-6 flex items-center rounded-sm border-1 border-black/3 bg-[#E4F6FEB2] p-4">
          <div className="mr-5 w-16">
            <Logo type="emblem" />
          </div>
          <div>
            <p className="text-lg font-medium">{title}</p>
            <p className="inline-block text-sm">
              You are saving <strong>{getPriceString(savings)}</strong> by
              shopping with this promotion.
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between gap-4">
          <Button variant="white" onClick={closeModal}>
            Keep Shopping
          </Button>
          <Button to={SHOP_ROUTES_PATH.checkout}>Checkout Now</Button>
        </div>
      </div>
    </Modal>
  );
};
