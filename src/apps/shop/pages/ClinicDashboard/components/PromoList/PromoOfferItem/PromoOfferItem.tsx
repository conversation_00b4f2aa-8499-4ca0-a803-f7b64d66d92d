import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { OfferType } from '@/types';
import { getPriceString } from '@/utils';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';

type PromoOfferItemProps = {
  offer: OfferType;
  onQuantityChange: (offer: OfferType, quantity: number) => void;
};

export const PromoOfferItem = ({
  offer,
  onQuantityChange,
}: PromoOfferItemProps) => {
  const { originalPrice, salePrice } = getProductOfferComputedData(offer);
  if (!salePrice) return null;

  return (
    <div className="grid w-full grid-cols-[auto_1fr_auto] items-center bg-white p-4">
      <div className="h-12">
        <img
          src={offer.vendor.imageUrl}
          className="h-full"
          alt={`${offer.vendor.name} logo`}
        />
      </div>
      <div className="mr-8 ml-5">
        <p className="max-w-96 text-sm font-medium text-wrap text-black">
          {offer.name}
        </p>
        <div className="flex items-center gap-4 divide-x-1 divide-solid divide-black/10">
          <span>
            <span className="text-xs text-black/65">SKU: </span>
            <span className="mr-3 text-xs font-medium text-black">
              {offer.vendorSku}
            </span>
          </span>
          <span className="text-xs text-black">{offer.vendor.name}</span>
        </div>
      </div>
      <div className="flex max-w-40 items-center gap-4">
        <div className="grid">
          {originalPrice > salePrice ? (
            <>
              <span className="text-lg leading-5 font-semibold">
                {getPriceString(salePrice)}
              </span>
              <span className="text-right text-sm font-medium text-gray-400 line-through">
                {getPriceString(originalPrice)}
              </span>
            </>
          ) : (
            <span className="text-sm font-medium">
              {getPriceString(salePrice)}
            </span>
          )}
        </div>
        <AddToCartInput
          originalAmount={offer.increments}
          minIncrement={offer.increments}
          onUpdate={({ amount }) => {
            onQuantityChange(offer, amount);
          }}
        />
      </div>
    </div>
  );
};
