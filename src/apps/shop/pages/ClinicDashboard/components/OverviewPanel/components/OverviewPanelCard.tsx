import { Divider, Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import styles from './OverviewPanelCard.module.css';
import { ReactNode } from 'react';

interface OverviewPanelCardProps {
  title: string;
  description: string;
  value: string;
  helpText: string | null;
  footer: ReactNode;
}

export const OverviewPanelCard = ({
  title,
  description,
  helpText,
  value,
  footer,
}: OverviewPanelCardProps) => {
  return (
    <div className={`${styles.container} w-full p-8`}>
      <Flex align="center" justify="space-between">
        <div>
          <Title order={3} lh="1" mb="0.75rem" c="#333">
            {title} {helpText ? <HelpTooltip message={helpText} /> : null}
          </Title>
          <Text lh="1" c="#333">
            {description}
          </Text>
        </div>
        <div>
          <Text c="#344054" size="40px" fw="500">
            {value}
          </Text>
        </div>
      </Flex>
      {footer ? (
        <>
          <Divider my="md" />
          <div>{footer}</div>
        </>
      ) : null}
    </div>
  );
};
