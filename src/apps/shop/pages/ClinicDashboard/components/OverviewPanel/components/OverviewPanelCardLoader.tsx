import { Flex } from '@/libs/ui/Flex/Flex';
import 'react-loading-skeleton/dist/skeleton.css';

import Skeleton from 'react-loading-skeleton';
import styles from './OverviewPanelCard.module.css';

export const OverviewPanelCardLoader = () => {
  return (
    <Flex className={styles.container} gap="5rem" p="2rem" align="center">
      <div className="w-[150px]">
        <div className="mb-3">
          <Skeleton height={22} />
        </div>
        <Skeleton height={16} />
      </div>
      <div className="w-[200px]">
        <Skeleton height={38} />
      </div>
    </Flex>
  );
};
