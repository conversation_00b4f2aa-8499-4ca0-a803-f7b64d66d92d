import { Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { OrderHistoryItem } from '@/apps/shop/pages/OrderHistory/components/OrderHistoryItem/OrderHistoryItem';
import { useOrderList } from '@/apps/shop/pages/OrderHistory/services/useOrderList';
import styles from './LastOrdersPanel.module.css';

export const LastOrdersPanel = () => {
  const { orders } = useOrderList({ limit: 4 });

  return (
    <CollapsiblePanel
      header={
        <Title order={4} px="1.5rem" py="1.3rem" fw="500">
          Last Orders
        </Title>
      }
      content={
        <Flex direction="column" p="1.5rem" bg="white">
          <Title order={4} fw="500">
            View your most recent orders.
          </Title>

          <Text size="md" mb="1.5rem">
            Click on any order to see full details.
          </Text>

          <div className="bg-[rgba(0,0,0,0.02)] p-4">
            {orders.length ? (
              orders.map((order) => (
                <div
                  key={order.id}
                  className={`mb-4 bg-[rgba(0,0,0,0.02)] ${styles.orderItem}`}
                >
                  <OrderHistoryItem order={order} isActive={false} />
                </div>
              ))
            ) : (
              <Text>No order created yet!</Text>
            )}
          </div>
        </Flex>
      }
      startOpen
    />
  );
};
