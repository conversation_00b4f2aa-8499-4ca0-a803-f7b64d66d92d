import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Button } from '@/libs/ui/Button/Button';
import { RebateType } from '@/types/common';
import { t } from 'i18next';

export const EstimateRebateCTA = ({
  promotion,
}: {
  promotion: RebateType['promotion'];
}) => {
  return (
    <div className="flex items-center gap-4">
      <span className="text-xs font-medium text-nowrap text-[#344054]">
        {t('client.dashboard.startEarning')}
      </span>
      <Button
        to={`${SHOP_ROUTES_PATH.search}?query=${promotion.keyword}`}
        size="sm"
        p="0 2.5rem"
      >
        {t('client.dashboard.shopNow')}
      </Button>
    </div>
  );
};
