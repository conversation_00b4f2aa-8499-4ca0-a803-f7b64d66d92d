.container {
  display: flex;
  flex-direction: column;

  .content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    box-shadow: none;
    border-radius: 0.5rem;
    /* TODO: Remove !important */
    background-color: transparent !important;
    &:has(.emptySearch) {
      justify-content: center;
    }
  }

  .contentTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 1rem;
    margin: 0.1rem 0.1rem 1.5rem;

    h3 {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 0;
      color: var(--mantine-color-dark-8);
    }
  }
}

.listTitle {
  padding: 0 1.5rem;
}

.listTitle {
  background-color: var(--mantine-color-light-blue-3);
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  position: sticky;
  top: 0;
  z-index: 500;
  flex-shrink: 0;
  height: 3rem;
  border-radius: 0.5rem 0.5rem 0 0;
}
