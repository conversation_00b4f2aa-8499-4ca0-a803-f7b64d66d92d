.box {
  padding: 2rem;
  min-width: 600px;
  max-width: 600px;

  & > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    column-gap: 1rem;
    margin-bottom: 1.75rem;
    justify-content: space-between;
    position: relative;
  }

  :global(.custom-error-field) {
    position: absolute;
    left: 0;
  }
}

.fields {
  & > div {
    width: calc(100% - 0.5rem);
  }
}

.btns {
  button {
    width: calc(100% / 3 - 0.5rem);
  }
}

.resetButton {
  width: 100%;
}
