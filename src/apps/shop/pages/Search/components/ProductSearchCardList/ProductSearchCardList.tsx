import { Grid, Loader, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { ProductCard } from '@/libs/products/components/ProductCard/ProductCard';
import type { ProductType } from '@/types';
import styles from './ProductSearchCardList.module.css';

type ProductSearchCardListProps = {
  isSearchLoading: boolean;
  productList: ProductType[];
  searchValue?: string;
  hasError?: boolean;
};

export const ProductSearchCardList = ({
  isSearchLoading,
  searchValue,
  productList,
  hasError,
}: ProductSearchCardListProps) => {
  const { t } = useTranslation();

  if (isSearchLoading) {
    return (
      <div className="loaderRoot">
        <Loader size="3rem" />
      </div>
    );
  }

  if (!productList.length) {
    if (!searchValue) {
      return (
        <div className={styles.emptySearch} data-testid="empty-search">
          <Text size="lg">{t('client.search.emptySearchQuery')}</Text>
        </div>
      );
    }

    return (
      <div className={styles.emptySearch} data-testid="empty-search">
        <Text size="lg">
          {hasError ? (
            t('apiErrors.general')
          ) : (
            <>
              {t('client.search.emptySearch')}

              <Text span fw="700">
                {` "${searchValue}"`}
              </Text>
            </>
          )}
        </Text>
      </div>
    );
  }

  return (
    <Grid>
      {productList.map((item) => {
        return (
          <Grid.Col key={item.id} span={{ base: 12, xs: 6, lg: 4, xl: 3 }}>
            <ProductCard data={item} />
          </Grid.Col>
        );
      })}
    </Grid>
  );
};
