type AddressType = {
  street: string;
  city: string;
  state: string;
  postalCode: string;
};

export type VendorLiteType = {
  id: string;
  name: string;
  type: string;
  imageUrl: string;
};

export type ClinicInfoType = {
  id: string;
  name: string;
  businessTaxId: string;
  phoneNumber: string;
  shippingAddress: AddressType;
  billingAddress: AddressType;
  sameAddress: boolean;
  hasAnyVendorConnected: boolean;
  primaryShoppingPreference: string | string[] | null;
  secondaryShoppingPreferences: string[] | null;
  practiceTypes: string[] | null;
  speciesFocus: string[] | null;
  examRoomsCount: number | null;
  fulltimeDvmCount: number | null;
  primaryDistributor: VendorLiteType | null;
  secondaryDistributor: VendorLiteType | null;
  preferredManufacturers: VendorLiteType[] | null;
};
