import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { LoadingOverlay, UnstyledButton } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useSettingsStore } from '@/apps/shop/stores/useSettingsStore/useSettingsStore';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';

import { BUDGET_TYPE } from '@/libs/cart/constants';
import styles from './BudgetForm.module.css';
import { DynamicBudgetForm } from './components/DynamicBudgetForm/DynamicBudgetForm';
import { StaticBudgetForm } from './components/StaticBudgetForm/StaticBudgetForm';

interface BudgetFormProps {
  onComplete: VoidFunction;
}

export const BudgetForm = ({ onComplete }: BudgetFormProps) => {
  const { getBudget, budget, updateBudget } = useSettingsStore();
  const [budgetStrategy, setBudgetStrategy] = useState<'STATIC' | 'DYNAMIC'>(
    'DYNAMIC',
  );

  const hasStaticType = budgetStrategy === BUDGET_TYPE.STATIC;

  useEffect(() => {
    if (budget) {
      setBudgetStrategy(budget.type);
    }
  }, [budget]);

  const { apiRequest: getBudgetApi, isLoading: isLoadingGet } = useAsyncRequest(
    {
      apiFunc: getBudget,
    },
  );

  useEffect(() => {
    getBudgetApi();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdateBudget = async (
    formData: import('../../interfaces').FormValues,
  ) => {
    await updateBudget(formData);
    onComplete();
  };

  return (
    <div>
      <LoadingOverlay visible={isLoadingGet} />
      <Flex justify="center" mb="md">
        <UnstyledButton
          onClick={() => {
            setBudgetStrategy(BUDGET_TYPE.STATIC);
          }}
          className={clsx(styles.budgetTypeButton, {
            [styles.budgetTypeButtonSelected]: hasStaticType,
          })}
        >
          Static Budget
        </UnstyledButton>
        <UnstyledButton
          onClick={() => {
            setBudgetStrategy(BUDGET_TYPE.DYNAMIC);
          }}
          className={clsx(styles.budgetTypeButton, {
            [styles.budgetTypeButtonSelected]: !hasStaticType,
          })}
        >
          Dynamic Budget
        </UnstyledButton>
      </Flex>

      {hasStaticType ? (
        <StaticBudgetForm onUpdate={handleUpdateBudget} budget={budget} />
      ) : (
        <DynamicBudgetForm onUpdate={handleUpdateBudget} budget={budget} />
      )}
    </div>
  );
};
