import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Resolver } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BudgetType } from '@/libs/cart/types';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { ApiErrorProps } from '@/types/utility';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Input } from '@/libs/form/Input';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import { integerMoneyMask } from '@/libs/form/masks';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { FormValues, StaticFormValues } from '../../../../interfaces';
import { STATIC_SCHEMA } from '../../constants';
import { transformValueToForm, transformValueToSend } from '../../utils';

interface StaticBudgetFormProps {
  onUpdate: (formData: FormValues) => Promise<void>;
  budget: BudgetType | null;
}

export const StaticBudgetForm = ({
  onUpdate,
  budget,
}: StaticBudgetFormProps) => {
  const { t } = useTranslation();
  const [includeExternalData, setIncludeExternalData] = useState(false);

  const getInitialValues = () => {
    if (budget && budget.type) {
      const transformedValues = transformValueToForm(budget);
      return {
        weeklyCogs: transformedValues.weeklyCogs,
        monthlyCogs: transformedValues.monthlyCogs,
        weeklyGa: transformedValues.weeklyGa,
        monthlyGa: transformedValues.monthlyGa,
        externalWeeklyCogs: transformedValues.externalWeeklyCogs,
        externalMonthlyCogs: transformedValues.externalMonthlyCogs,
      };
    } else {
      return {
        weeklyCogs: 0,
        monthlyCogs: 0,
        weeklyGa: 0,
        monthlyGa: 0,
        externalWeeklyCogs: 0,
        externalMonthlyCogs: 0,
      };
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
    reset,
  } = useForm<StaticFormValues>({
    resolver: yupResolver(
      STATIC_SCHEMA,
    ) as unknown as Resolver<StaticFormValues>,
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: getInitialValues(),
  });

  useEffect(() => {
    if (budget) {
      setIncludeExternalData(budget.includeExternalData);
      reset(getInitialValues());
    }
  }, [budget, reset]);

  const onSubmit = async (values: StaticFormValues) => {
    try {
      const formData = transformValueToSend({
        ...values,
        includeExternalData,
        type: 'STATIC',
      } as FormValues);

      await onUpdate(formData);

      successNotification(
        t('client.settings.updatedSettings', {
          tab: t('client.settings.budget.title'),
        }),
      );
    } catch (error) {
      defaultFormErrorHandler(error as ApiErrorProps, setError);
      console.error('Error submitting form:', error);
    }
  };

  return (
    <form id="static-form" onSubmit={handleSubmit(onSubmit)}>
      <Flex gap="1rem" mb="24px">
        <div className="flex-1">
          <Input
            mask={integerMoneyMask}
            label={
              <>
                {t('client.settings.budget.weeklyCOGS')}{' '}
                <HelpTooltip message={t('client.settings.budget.COGSHelp')} />
              </>
            }
            {...register('weeklyCogs')}
            error={errors.weeklyCogs?.message}
          />
        </div>
        <div className="flex-1">
          <Input
            mask={integerMoneyMask}
            label={
              <>
                {t('client.settings.budget.weeklyGA')}{' '}
                <HelpTooltip message={t('client.settings.budget.GAHelp')} />
              </>
            }
            {...register('weeklyGa')}
            error={errors.weeklyGa?.message}
          />
        </div>
      </Flex>
      <Flex gap="1rem" mb="24px">
        <div className="flex-1">
          <Input
            mask={integerMoneyMask}
            label={t('client.settings.budget.monthlyCOGS')}
            {...register('monthlyCogs')}
            error={errors.monthlyCogs?.message}
          />
        </div>
        <div className="flex-1">
          <Input
            mask={integerMoneyMask}
            label={t('client.settings.budget.monthlyGA')}
            {...register('monthlyGa')}
            error={errors.monthlyGa?.message}
          />
        </div>
      </Flex>

      {includeExternalData ? (
        <Flex gap="1rem" my="24px">
          <div className="flex-1">
            <Input
              mask={integerMoneyMask}
              label={t('client.settings.budget.externalWeeklyCogs')}
              {...register('externalWeeklyCogs')}
              error={errors.externalWeeklyCogs?.message}
            />
          </div>
          <div className="flex-1">
            <Input
              mask={integerMoneyMask}
              label={t('client.settings.budget.externalMonthlyCogs')}
              {...register('externalMonthlyCogs')}
              error={errors.externalMonthlyCogs?.message}
            />
          </div>
        </Flex>
      ) : null}

      <div className="mb-6">
        <Checkbox
          label={t('client.settings.budget.costsInCOGS')}
          checked={includeExternalData}
          onChange={() => {
            setIncludeExternalData(!includeExternalData);
          }}
        />
      </div>

      <Button form="static-form" variant="secondary" disabled={!isValid}>
        {t('common.save')}
      </Button>
    </form>
  );
};
