import { FormValues } from '../../interfaces';
import { BudgetType } from '@/libs/cart/types';

const clearIntegerValue = (value: number | string) => {
  const integerValue = +value?.toString()?.replace(/\D/g, '');
  return integerValue ? integerValue : 0;
};

export function transformValueToSend({
  avgTwoWeeksSales,
  includeExternalData,
  monthToDateSales,
  targetCogsPercent,
  targetGaPercent,
  monthlyCogs,
  externalMonthlyCogs,
  monthlyGa,
  externalWeeklyCogs,
  weeklyCogs,
  weeklyGa,
  type,
}: FormValues): BudgetType {
  return {
    type,
    avgTwoWeeksSales: clearIntegerValue(avgTwoWeeksSales),
    monthToDateSales: clearIntegerValue(monthToDateSales),
    monthlyCogs: clearIntegerValue(monthlyCogs),
    monthlyGa: clearIntegerValue(monthlyGa),
    targetCogsPercent: targetCogsPercent
      ? +(targetCogsPercent / 100).toFixed(2)
      : 0,
    targetGaPercent: targetGaPercent ? +(targetGaPercent / 100).toFixed(2) : 0,
    weeklyCogs: clearIntegerValue(weeklyCogs),
    weeklyGa: clearIntegerValue(weeklyGa),
    includeExternalData: includeExternalData,
    externalMonthlyCogs: clearIntegerValue(externalMonthlyCogs),
    externalWeeklyCogs: clearIntegerValue(externalWeeklyCogs),
  };
}

export function transformValueToForm({
  avgTwoWeeksSales,
  includeExternalData,
  monthToDateSales,
  targetCogsPercent,
  targetGaPercent,
  monthlyCogs,
  externalMonthlyCogs,
  monthlyGa,
  externalWeeklyCogs,
  weeklyCogs,
  weeklyGa,
  type,
}: BudgetType): FormValues {
  return {
    type,
    avgTwoWeeksSales: Math.floor(avgTwoWeeksSales),
    monthToDateSales: Math.floor(monthToDateSales),
    monthlyCogs: Math.floor(monthlyCogs),
    monthlyGa: Math.floor(monthlyGa),
    targetCogsPercent: targetCogsPercent
      ? +(targetCogsPercent * 100).toFixed(2)
      : 0,
    targetGaPercent: targetGaPercent ? +(targetGaPercent * 100).toFixed(2) : 0,
    weeklyCogs: Math.floor(weeklyCogs),
    weeklyGa: Math.floor(weeklyGa),
    includeExternalData,
    externalMonthlyCogs: Math.floor(externalMonthlyCogs),
    externalWeeklyCogs: Math.floor(externalWeeklyCogs),
  };
}
