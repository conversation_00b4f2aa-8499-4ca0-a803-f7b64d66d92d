import { BudgetType } from '@/libs/cart/types';

export interface FormValues extends Omit<BudgetType, 'includeExternalData'> {
  includeExternalData: boolean;
}

export interface StaticFormValues {
  weeklyCogs: number;
  monthlyCogs: number;
  weeklyGa: number;
  monthlyGa: number;
  externalWeeklyCogs?: number;
  externalMonthlyCogs?: number;
}

export interface DynamicFormValues {
  targetCogsPercent: number;
  targetGaPercent: number;
  avgTwoWeeksSales: number;
  monthToDateSales: number;
  externalWeeklyCogs?: number;
  externalMonthlyCogs?: number;
}
