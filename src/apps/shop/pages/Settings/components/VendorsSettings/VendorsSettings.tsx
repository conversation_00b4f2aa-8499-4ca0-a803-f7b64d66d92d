import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useClinicInfo } from '../../services/useClinicInfo';
import { VendorsSettingsForm } from './components/VendorsSettingsForm/VendorsSettingsForm';
import { VendorCard } from './components/VendorCard';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { Button } from '@/libs/ui/Button/Button';

interface VendorsSettingsProps {
  isEditing: boolean;
  onComplete: VoidFunction;
  startEditing: VoidFunction;
}
export const VendorsSettings = ({
  isEditing,
  onComplete,
  startEditing,
}: VendorsSettingsProps) => {
  const { clinicInfo, isLoading, fetchClinicInfo } = useClinicInfo();

  if (isLoading) {
    return (
      <div className="relative p-6">
        <ContentLoader />
      </div>
    );
  }

  if (!clinicInfo) {
    return null;
  }

  const { primaryDistributor, secondaryDistributor, preferredManufacturers } =
    clinicInfo;

  if (isEditing) {
    return (
      <VendorsSettingsForm
        clinicId={clinicInfo.id}
        onComplete={() => {
          fetchClinicInfo();
          onComplete();
        }}
        defaultValues={{
          primaryDistributorId: primaryDistributor?.id || '',
          secondaryDistributorId: secondaryDistributor?.id || '',
          preferredManufacturerIds:
            preferredManufacturers?.map(({ id }) => id) || [],
        }}
      />
    );
  }

  const hasVendors =
    primaryDistributor ||
    secondaryDistributor ||
    (preferredManufacturers && preferredManufacturers.length > 0);

  if (!hasVendors) {
    return (
      <Flex
        mt="md"
        py="40px"
        px="24px"
        direction="column"
        align="center"
        bg="#F8F9FB"
        style={{ borderRadius: '4px' }}
      >
        <Text size="16px" fw="500" ta="center" c="#344054">
          You haven’t selected any preferred vendors yet
        </Text>
        <Text size="14px" mt="8px" mb="16px" ta="center" c="#555F74" lh="1.5">
          Set your preferred vendors to customize your experience. Get started
          and personalize your purchasing workflow.
        </Text>
        <Button
          onClick={startEditing}
          variant="secondary"
          style={{ width: 'auto' }}
        >
          Select Preferred Vendors
        </Button>
      </Flex>
    );
  }

  return (
    <Flex direction="column" gap="12px" mt="12px">
      {primaryDistributor ? (
        <VendorCard
          vendor={primaryDistributor}
          typeLabel="Primary Distributor"
        />
      ) : null}
      {secondaryDistributor ? (
        <VendorCard
          vendor={secondaryDistributor}
          typeLabel="Secondary Distributor"
        />
      ) : null}
      {preferredManufacturers && preferredManufacturers.length > 0
        ? preferredManufacturers.map((vendor) => (
            <VendorCard key={vendor.id} vendor={vendor} typeLabel="Preferred" />
          ))
        : null}
    </Flex>
  );
};
