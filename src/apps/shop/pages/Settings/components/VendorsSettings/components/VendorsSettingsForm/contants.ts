import * as Yup from 'yup';

export const SCHEMA = Yup.object({
  primaryDistributorId: Yup.string()
    .max(255)
    .test(
      'not-same-as-secondary',
      'Primary distributor must be different from secondary distributor',
      function (value) {
        const { secondaryDistributorId } = this.parent;
        return (
          !value || !secondaryDistributorId || value !== secondaryDistributorId
        );
      },
    ),
  secondaryDistributorId: Yup.string()
    .max(255)
    .test(
      'not-same-as-primary',
      'Secondary distributor must be different from primary distributor',
      function (value) {
        const { primaryDistributorId } = this.parent;
        return (
          !value || !primaryDistributorId || value !== primaryDistributorId
        );
      },
    ),
  preferredManufacturerIds: Yup.array(),
});
