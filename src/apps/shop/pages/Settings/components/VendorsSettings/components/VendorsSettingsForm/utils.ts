import { VendorType } from '@/types';

export const getConnectedVendorOptionsByType = (vendors: VendorType[]) => {
  return vendors.reduce<{
    manufactureresOptions: {
      label: string;
      value: string;
    }[];
    distributorsOptions: { label: string; value: string }[];
  }>(
    (acc, vendor) => {
      const option = {
        label: vendor.name,
        value: vendor.id,
      };
      if (vendor.status === 'disconnected') {
        return acc;
      }

      if (vendor.type === 'manufacturer') {
        acc.manufactureresOptions.push(option);
      } else {
        acc.distributorsOptions.push(option);
      }

      return acc;
    },
    {
      manufactureresOptions: [],
      distributorsOptions: [],
    },
  );
};
