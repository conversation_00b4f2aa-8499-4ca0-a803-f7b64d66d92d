import { ClinicInfoType } from '@/apps/shop/pages/Settings/types';
import { Input } from '@/libs/form/Input';
import { Select } from '@/libs/form/Select';
import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { US_STATES } from '@/constants';
import { einMask, phoneMask, postalCodeMask } from '@/libs/form/masks';
import { Button } from '@/libs/ui/Button/Button';
import { post } from '@/libs/utils/api';
import { errorNotification, successNotification } from '@/utils';

const SCHEMA = Yup.object({
  name: Yup.string().required('Name is required').max(255),
  businessTaxId: Yup.string()
    .required('Business Tax ID is required')
    .matches(/^\d{2}-\d{7}$/, 'Invalid EIN format'),
  phoneNumber: Yup.string().required('Phone number is required').max(255),
  address: Yup.object({
    street: Yup.string().required('Street is required').max(255),
    city: Yup.string().required('City is required').max(255),
    state: Yup.string().required('State is required').max(255),
    postalCode: Yup.string()
      .required('Postal code is required')
      .matches(/^\d{5}(-\d{4})?$/, 'Invalid postal code'),
  }),
});

type ApiErrorResponse = {
  data: {
    message: string;
    errors?: Record<string, string[]>;
  };
};

type ClinicInformationRequest = {
  name: string;
  businessTaxId: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
  };
};

interface ClinicInfoFormProps {
  clinicInfo: ClinicInfoType;
  onComplete: VoidFunction;
}
export const ClinicInfoForm = ({
  clinicInfo,
  onComplete,
}: ClinicInfoFormProps) => {
  const { id, name, businessTaxId, shippingAddress, phoneNumber } = clinicInfo;

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
  } = useForm({
    resolver: yupResolver(SCHEMA),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: {
      businessTaxId,
      name,
      phoneNumber,
      address: shippingAddress,
    },
  });

  const onSubmit = async (data: ClinicInformationRequest) => {
    try {
      await post({
        url: `/clinics/${id}`,
        method: 'PATCH',
        body: {
          ...data,
        },
      });

      successNotification('Clinic info was updated');

      onComplete();
    } catch (error) {
      const apiError = error as ApiErrorResponse;

      if (apiError?.data?.errors) {
        Object.keys(apiError.data.errors).forEach((errorKey) => {
          const fieldKey = errorKey as keyof ClinicInformationRequest;
          const messages = apiError.data.errors?.[fieldKey];
          if (messages && messages.length > 0) {
            setError(fieldKey, { message: messages[0] });
          }
        });
      } else {
        errorNotification();
      }
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="rounded border border-black/[0.04] bg-gradient-to-b from-black/[0.02] to-black/[0.02] p-6"
    >
      <div className="rounded border border-black/[0.04] bg-white p-6">
        <Text size="16px" fw="500" c="#344054">
          Clinic Information
        </Text>
        <Divider my="md" />
        <Flex gap="1rem" mb="24px">
          <div className="flex-1">
            <Input
              label="Clinic Name"
              {...register('name')}
              error={errors.name?.message}
            />
          </div>
          <div className="flex-1">
            <Input
              label="Business Tax ID"
              disabled
              {...register('businessTaxId')}
              mask={einMask}
              error={errors.businessTaxId?.message}
            />
          </div>
        </Flex>

        <div className="mb-6">
          <Input
            label="Street"
            {...register('address.street')}
            error={errors.address?.street?.message}
          />
        </div>
        <Flex gap="1rem" mb="24px">
          <div className="flex-1">
            <Input
              label="City"
              {...register('address.city')}
              error={errors.address?.city?.message}
            />
          </div>
          <div className="flex-1">
            <Select
              label="State"
              {...register('address.state')}
              error={errors.address?.state?.message}
              options={US_STATES}
            />
          </div>
        </Flex>
        <div className="mb-6">
          <Input
            label="Postal Code"
            {...register('address.postalCode')}
            mask={postalCodeMask}
            error={errors.address?.postalCode?.message}
          />
        </div>
        <div>
          <Input
            label="Phone Number"
            {...register('phoneNumber')}
            mask={phoneMask}
            error={errors.phoneNumber?.message}
          />
        </div>
      </div>
      <div className="mt-3">
        <Button variant="secondary" disabled={!isValid}>
          Save
        </Button>
      </div>
    </form>
  );
};
