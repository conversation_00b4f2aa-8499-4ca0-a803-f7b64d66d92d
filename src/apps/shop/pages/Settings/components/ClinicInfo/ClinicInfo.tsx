import { Divider, Text } from '@mantine/core';
import { useClinicInfo } from '../../services/useClinicInfo';
import { ClinicInfoForm } from './ClinicInfoForm/ClinicInfoForm';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { phoneMask } from '@/libs/form/masks';

interface ClinicInfoProps {
  isEditing: boolean;
  onComplete: VoidFunction;
}
export const ClinicInfo = ({ isEditing, onComplete }: ClinicInfoProps) => {
  const { clinicInfo, isLoading, fetchClinicInfo } = useClinicInfo();

  if (isLoading) {
    return (
      <div className="relative p-6">
        <ContentLoader />
      </div>
    );
  }

  if (!clinicInfo) {
    return null;
  }

  if (isEditing) {
    return (
      <ClinicInfoForm
        clinicInfo={clinicInfo}
        onComplete={() => {
          fetchClinicInfo();
          onComplete();
        }}
      />
    );
  }

  return (
    <div className="mt-3 rounded-sm border border-solid border-[#3958D4] p-6">
      <Text c="#666" size="14px" mb="14px">
        Clinic Name
      </Text>
      <Text c="#344054" size="24px" fw="500">
        {clinicInfo.name}
      </Text>
      <Divider my="24px" />
      <Text c="#666" mb="14px">
        Phone Number:{' '}
        <Text c="#333" fw="700" span>
          {phoneMask(clinicInfo.phoneNumber)}
        </Text>
      </Text>
      <Text c="#666" mb="14px">
        Business Tax ID (EIN):{' '}
        <Text c="#333" fw="700" span>
          {clinicInfo.businessTaxId}
        </Text>
      </Text>
      <Text c="#666" mb="14px">
        Address:{' '}
        <Text c="#333" fw="700" span>
          {`${clinicInfo.shippingAddress.street}, ${clinicInfo.shippingAddress.city}, ${clinicInfo.shippingAddress.state} ${clinicInfo.shippingAddress.postalCode}`}
        </Text>
      </Text>
    </div>
  );
};
