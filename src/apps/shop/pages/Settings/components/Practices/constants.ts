export const shoppingPreferencesOptions = [
  { value: 'brand_loyalty', label: 'Brand loyalty' },
  { value: 'best_value_for_money', label: 'Best value for money' },
  { value: 'clinical_outcome_focused', label: 'Clinical outcome focused' },
  { value: 'cost_first_decision_maker', label: 'Cost first decision maker' },
  { value: 'convenience_driven', label: 'Convenience driven' },
  { value: 'distributor_loyalty', label: 'Distributor loyalty' },
  { value: 'gpo_products_preferred', label: 'GPO products preferred' },
  { value: 'incentive_motivated', label: 'Incentive motivated' },
  { value: 'innovation', label: 'Innovation' },
  { value: 'product_familiarity', label: 'Product familiarity' },
  {
    value: 'relationship_based_purchasing',
    label: 'Relationship based purchasing',
  },
  {
    value: 'willing_to_try_new_suppliers',
    label: 'Willing to try new suppliers',
  },
  {
    value: 'corporate_purchasing_guidelines',
    label: 'Corporate purchasing guidelines',
  },
];
