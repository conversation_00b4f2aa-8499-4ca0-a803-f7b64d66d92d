import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { CartSummary } from '@/libs/cart/components/CartSummary/CartSummary';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Divider, Image, Text } from '@mantine/core';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Navigate, useNavigate } from 'react-router-dom';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import { Alert } from '@/libs/ui/Alert/Alert';
import InfoIcon from './assets/info-icon.svg?react';
import styles from './Checkout.module.css';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';
import { CheckoutSuccessModal } from './components/CheckoutSuccessModal/CheckoutSuccessModal';
import { useEffect } from 'react';
import type { CheckoutResponseType } from '@/apps/shop/stores/useCartStore/type';
import { BreadCrumbs } from '@/libs/ui/BreadCrumbs/BreadCrumbs';

export const Checkout = () => {
  const { vendors, fetchCart, checkout } = useCartStore();
  const { openModal } = useModalStore();
  const navigate = useNavigate();

  const {
    apiRequest: onCheckout,
    isLoading: isCheckoutLoading,
    result: checkoutResponse,
  } = useAsyncRequest<void, CheckoutResponseType>({
    apiFunc: async () => await checkout(),
  });

  const handleCheckout = () => {
    onCheckout();
  };

  useEffect(() => {
    if (checkoutResponse?.id) {
      openModal({
        name: MODAL_NAME.CHECKOUT_SUCCESS,
        ...checkoutResponse,
      });
    }

    return () => {
      if (checkoutResponse?.id) {
        fetchCart();
      }
    };
  }, [checkoutResponse, openModal, fetchCart]);

  if (!vendors.length && !checkoutResponse?.id) {
    return <Navigate to={SHOP_ROUTES_PATH.cart} />;
  }

  return (
    <div className={'mainSection'}>
      <PageHeader title="Checkout" />
      <div className="mb-2">
        <BreadCrumbs
          items={[
            { path: SHOP_ROUTES_PATH.cart, name: 'Cart', search: '' },
            { path: '#', name: 'Checkout', search: '' },
          ]}
        />
      </div>

      <Flex gap="md" wrap="wrap">
        <Flex
          bg="#fff"
          p="1.5rem"
          className={styles.vendorWrap}
          direction="column"
        >
          <Text size="1rem" mb="md" fw="500">
            Product Summary
          </Text>
          {vendors.map(({ id, name, imageUrl, items }) => (
            <div key={id} className="mb-4">
              <CollapsiblePanel
                header={
                  <Flex align="center" pr="5rem">
                    <Image
                      src={imageUrl}
                      alt={name}
                      fallbackSrc={defaultProductImgUrl}
                      h={42}
                      title={name}
                    />
                  </Flex>
                }
                content={
                  <div className="p-4">
                    <table className={styles.table}>
                      <tbody>
                        {items.map(
                          ({
                            quantity,
                            product,
                            subtotal,
                            price,
                            productOfferId,
                          }) => (
                            <tr key={productOfferId}>
                              <td width="100%">
                                <Text
                                  miw="50%"
                                  fw="500"
                                  className={styles.productName}
                                >
                                  {product?.name}
                                </Text>
                              </td>
                              <td>
                                <Text c="#666">
                                  Quantity:{' '}
                                  <Text span c="#333" fw="500">
                                    {quantity}
                                  </Text>
                                </Text>
                                <Divider orientation="vertical" mx="sm" />
                              </td>
                              <td>
                                <Divider
                                  orientation="vertical"
                                  mx="0.5rem"
                                  h="1rem"
                                />
                              </td>
                              <td>
                                <Text c="#666">
                                  Price:{' '}
                                  <Text span c="#333" fw="500">
                                    {getPriceString(price)}
                                  </Text>
                                </Text>
                              </td>
                              <td>
                                <Divider
                                  orientation="vertical"
                                  mx="0.5rem"
                                  h="1rem"
                                />
                              </td>
                              <td>
                                <Text c="#666">
                                  Net total:{' '}
                                  <Text span c="#333" fw="500">
                                    {getPriceString(subtotal)}
                                  </Text>
                                </Text>
                              </td>
                            </tr>
                          ),
                        )}
                      </tbody>
                    </table>
                  </div>
                }
                startOpen
              />
            </div>
          ))}
          <Text mt="auto" size="12px" c="#555F74" lh="1.5">
            <Text size="12px" c="#222" fw="500" span>
              Please order earlier when possible to avoid delays.
            </Text>
            <br />
            Orders placed within 1 hour of a vendor’s cutoff time may not
            transmit in time for same-day processing.
          </Text>
        </Flex>
        <div className={styles.cartSummaryWrap}>
          <CartSummary
            isLoading={isCheckoutLoading}
            headerAction={{
              label: 'Edit Cart',
              handleClick: () => {
                navigate(SHOP_ROUTES_PATH.cart);
              },
            }}
            action={{
              onAction: handleCheckout,
              isActionBlocked: false,
              actionButtonText: 'Place Order',
            }}
            extraInfo={
              <>
                <Text size="xs" mb="md">
                  Applicable taxes and fees are determined by each vendor and
                  will be displayed on your final invoice. All orders will be
                  shipped to the address on file with each vendor.
                </Text>
                <Alert icon={<InfoIcon />}>
                  <Text>
                    Placed orders can not be edited. Please verify all items are
                    correct at checkout.
                  </Text>
                </Alert>
              </>
            }
          />
        </div>
      </Flex>
      <CheckoutSuccessModal />
    </div>
  );
};
