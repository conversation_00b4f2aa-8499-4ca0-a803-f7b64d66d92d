.vendorWrap {
  width: 68%;
  flex-grow: 1;
  border: 1px solid rgba(208, 213, 221, 0.31);
  border-radius: 8px;
}

.table {
  width: 100%;
  margin-bottom: 0.25rem;
  border-collapse: collapse;
  min-width: 0;

  td {
    white-space: nowrap;
    padding-bottom: 0.75rem;
    border: none;
    vertical-align: top;
  }

  .productName {
    max-width: 400px;
    white-space: wrap;
    padding-right: 0.75rem;
  }

  tr:not(:first-child) td {
    padding-top: 0.75rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.cartSummaryWrap {
  max-width: 280px;
  width: 30%;
  flex-shrink: 0;
}

@media (max-width: 1280px) {
  .vendorWrap {
    width: 100%;
  }

  .cartSummaryWrap {
    width: 100%;
    max-width: 100%;
  }

  .productName {
    max-width: 600px;
  }
}
