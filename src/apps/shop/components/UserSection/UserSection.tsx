import { Avatar, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import styles from './UserSection.module.css';
import { UserSectionMenu } from './components/UserSectionMenu/UserSectionMenu';

export const UserSection = () => {
  const { user } = useAuthStore();

  return user ? (
    <Flex className={styles.container} align="center">
      <div className="mr-2">
        <Avatar radius="xl" />
      </div>
      <div className={styles.infoWrap}>
        <Text c="#161924" size="xs" lh="xl" fw="500">
          {user.name}
        </Text>
        <Text c="#5A5C66" size="xs" lh="lg">
          {user.email}
        </Text>
      </div>
      <UserSectionMenu />
    </Flex>
  ) : null;
};
