import { Resolver, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';

import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';

import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import { useModalStore } from '@/apps/shop/stores/useModalStore';

const SCHEMA = Yup.object().shape({
  currentPassword: Yup.string().required('Current password is required'),
  newPassword: Yup.string()
    .min(8, 'New password must be at least 8 characters')
    .required('New password is required'),
  confirmNewPassword: Yup.string()
    .oneOf([Yup.ref('newPassword')], 'Passwords must match')
    .required('Confirm password is required'),
});

type FormValues = {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
};

export const ChangePasswordModal = () => {
  const { t } = useTranslation();

  const { closeModal } = useModalStore();
  const { register, handleSubmit, reset, formState, setError } =
    useForm<FormValues>({
      resolver: yupResolver(SCHEMA) as unknown as Resolver<FormValues>,
      mode: 'all',
      reValidateMode: 'onChange',
    });

  const { apiRequest: handleSave, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      await post({
        url: '/user/password',
        body: values,
        method: 'PATCH',
      });

      successNotification('Password was updated');
      reset();
      closeModal();
    }),
    // TODO: Analyze server error handling
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  const errors = formState.errors;

  return (
    <Modal name={MODAL_NAME.CHANGE_PASSWORD} size="lg">
      <div className="bg-white">
        <form onSubmit={handleSave}>
          <Flex justify="center">
            <Flex direction="column" bg="#fff" w="600px">
              <Text size="1.2rem" fw="500">
                Change Password
                <Divider my="md" />
              </Text>
              <Flex direction="column" gap="md">
                <Flex gap="1rem">
                  <Input
                    id="name"
                    label="Current password"
                    type="password"
                    error={errors.currentPassword?.message}
                    size="lg"
                    {...register('currentPassword')}
                  />
                </Flex>
                <Flex gap="1rem">
                  <Input
                    id="name"
                    label="New password"
                    type="password"
                    error={errors.newPassword?.message}
                    size="lg"
                    {...register('newPassword')}
                  />

                  <Input
                    id="name"
                    label="Confirm new password"
                    type="password"
                    error={errors.confirmNewPassword?.message}
                    size="lg"
                    {...register('confirmNewPassword')}
                  />
                </Flex>
                <Flex mt="0.5rem" gap="1rem">
                  <Button type="button" variant="white" onClick={closeModal}>
                    {t('common.cancel')}
                  </Button>
                  <Button
                    variant="secondary"
                    loading={isLoading}
                    disabled={!formState.isValid}
                  >
                    Update password
                  </Button>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </form>
      </div>
    </Modal>
  );
};
