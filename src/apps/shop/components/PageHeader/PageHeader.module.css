.pageHeaderRoot {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 2rem;
  margin-bottom: 2rem;
  width: 100%;

  .titleSection {
    position: relative;
    display: flex;
    flex-direction: column;
    color: var(--mantine-color-dark-8);

    & > .title {
      font-weight: 700;
      line-height: 2.375rem;
    }

    & > .description {
      line-height: 1.5rem;
    }
  }

  .backBtn {
    position: absolute;
    left: -2rem;
    top: 0.25rem;

    svg path {
      transition: stroke 0.3s;
    }

    &:hover {
      svg path {
        stroke: var(--mantine-primary-color-5);
      }
    }
  }

  .childrenRoot {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    gap: 2rem;
  }
}

@media (min-width: 900px) {
  .pageHeaderRoot {
    flex-direction: row;
  }
}
