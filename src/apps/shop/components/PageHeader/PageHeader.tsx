import { PropsWithChildren } from 'react';
import { Title, Text } from '@mantine/core';
import clsx from 'clsx';

import styles from './PageHeader.module.css';

interface Props extends PropsWithChildren {
  title: string;
  description?: string;
  contentRootClass?: string;
}

export const PageHeader = ({
  title,
  description,
  contentRootClass,
  children,
}: Props) => {
  return (
    <div className={styles.pageHeaderRoot}>
      <div className={styles.titleSection}>
        <Title order={1} className={styles.title}>
          {title}
        </Title>
        {description && (
          <Text size="lgMd" className={styles.description}>
            {description}
          </Text>
        )}
      </div>
      <div className={clsx(contentRootClass ?? styles.childrenRoot)}>
        {children}
      </div>
    </div>
  );
};
