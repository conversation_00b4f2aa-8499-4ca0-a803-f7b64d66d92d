.sidebarRoot {
  position: sticky;
  top: 0;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 255px;
  height: 100vh;
  padding: 0 1rem;
  background-color: var(--mantine-color-light-blue-2);

  & > .divider {
    width: 100%;
    height: 1px;
  }
}

.name {
  display: flex;
  align-items: center;
  column-gap: 0.75rem;
  height: 21px;
}

.truncatedText {
  max-width: 200px;
}

.tabsSectionRoot {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  margin: 3rem 0 2rem;

  & > .tabsSection {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    & .tab {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      height: 40px;
      padding: 0 1rem;
      color: var(--mantine-color-dark-5);
      cursor: pointer;
      text-decoration: none;
      border-radius: 0.75rem;

      &:hover {
        background-color: var(--mantine-color-light-blue-4);
        color: var(--mantine-color-dark-9);

        & > svg {
          & > g {
            opacity: 1;
          }
        }
      }
    }

    & > .logout {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      height: 40px;
      padding: 0 1rem;
    }

    & .selectedTab {
      background-color: var(--mantine-color-light-blue-4);
      color: var(--mantine-color-dark-9);

      & > svg {
        & > g {
          opacity: 1;
        }
      }
    }

    .nestedLinks {
      padding: 0.75rem;
      border-radius: 0.5rem;
      background-color: var(--mantine-color-light-blue-4);

      .tab {
        margin-left: 1.2rem;
      }

      .clinicName {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0 0.25rem;

        & > div {
          max-width: 150px;
        }
      }
    }
  }
}

.settings {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--mantine-color-dark-9);
  cursor: pointer;
  text-decoration: none;
  margin-left: 1rem;
}

.lock {
  background-image: url('/src/assets/images/sidebar/lock-sidebar-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  object-fit: contain;
  height: calc(100vh - 500px);
  min-height: 300px;
  width: calc(100% + 2rem);
  margin-left: -1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  row-gap: 1rem;

  p {
    max-width: 180px;
    margin: 0 auto;
    text-align: center;
  }
}
