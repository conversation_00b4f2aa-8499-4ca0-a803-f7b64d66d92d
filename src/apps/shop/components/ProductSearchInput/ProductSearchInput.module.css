.container {
  position: relative;
  width: 100%;
}

.searchInput {
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  background-color: white;
  padding: 0.5rem 1rem;
  padding-right: 2.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out;
}

.loadingText {
  padding: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #6b7280;
}

.productName {
  font-weight: 500;
  color: #111827;
}

.productDescription {
  font-size: 0.75rem;
  line-height: 1rem;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
