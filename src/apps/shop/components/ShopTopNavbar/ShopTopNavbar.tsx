import { Divider, Flex, Text } from '@mantine/core';
import { ProductSearchInput } from '@/apps/shop/components/ProductSearchInput/ProductSearchInput';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useNavigate } from 'react-router-dom';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Button } from '@/components';
import { getPriceString } from '@/utils';
import CartSummaryIcon from '@/assets/images/cart/cart-summary.svg?react';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import styles from './ShopTopNavbar.module.css';
import { TopNavbar } from '@/libs/ui/TopNavbar/TopNavbar';
import { SwapClinic } from '@/libs/clinics/components/SwapClinic/SwapClinic';

interface ShopTopNavbarProps {
  showCart?: boolean;
}
export const ShopTopNavbar = ({ showCart = true }: ShopTopNavbarProps) => {
  const navigate = useNavigate();
  // TODO: Add state for cart loading on store
  const { isCartLoading, subtotal, uniqueItemsCount } = useCartStore();
  const { clinic } = useClinicStore();
  const { account } = useAccountStore();

  const handleGoToCart = () => {
    navigate(SHOP_ROUTES_PATH.cart);
  };

  if (!clinic || !account) {
    return null;
  }

  return (
    <TopNavbar>
      <SwapClinic current={clinic} list={account.clinics} />
      <div className="flex min-w-lg gap-6">
        <ProductSearchInput />
        {showCart ? (
          <Flex gap="md" align="center" className={styles.cartInfo}>
            <Flex gap="md" pl="1rem">
              <Text fw="700">{getPriceString(subtotal)}</Text>
              <Divider c="dark.3" orientation="vertical" />
              <Text fw="700">{uniqueItemsCount ?? 0}</Text>
            </Flex>
            <Button
              p="0"
              w="40"
              h="40"
              onClick={handleGoToCart}
              loading={isCartLoading}
            >
              <CartSummaryIcon />
            </Button>
          </Flex>
        ) : null}
      </div>
    </TopNavbar>
  );
};
