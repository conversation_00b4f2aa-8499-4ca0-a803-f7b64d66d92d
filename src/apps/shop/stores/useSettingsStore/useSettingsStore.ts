import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { get, post } from '@/libs/utils/api';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { BudgetType } from '@/libs/cart/types';
import { createStore } from '@/utils';

import { Actions, State } from './types';

export const useSettingsStore = createStore<State & Actions>()(
  immer(
    devtools((set) => ({
      budget: null,
      controlledDrugs: null,
      getBudget: async () => {
        const clinicId = useClinicStore.getState().clinic?.id;

        if (!clinicId) {
          return;
        }

        const budget = await get<BudgetType>({
          url: `/clinics/${clinicId}/budget-settings`,
        });

        set({ budget });
      },
      updateBudget: async (values: BudgetType) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        const response = await post<BudgetType>({
          url: `/clinics/${clinicId}/budget-settings`,
          body: values,
          method: 'PUT',
        });

        set({ budget: response });
      },
    })),
  ),
);
