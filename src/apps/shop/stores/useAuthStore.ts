import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createStore } from '@/utils';
import { Account, UserType } from '@/types/common';
import { deleteApi, get as getApi } from '@/libs/utils/api';

interface State {
  user: UserType | null;
}

interface Actions {
  setUser: (user: UserType) => void;
  getGpo: () => Account['gpo'];
  updateUser: (user: Partial<UserType>) => void;
  logout: () => void;
  leaveImpersonation: () => void;
}

export const useAuthStore = createStore<State & Actions>()(
  immer(
    devtools((set, get) => ({
      user: null,
      logout: async () => {
        await deleteApi({
          url: '/sessions',
          withApi: false,
        });
        set({ user: null });
      },
      leaveImpersonation: async () => {
        const response = await getApi<{
          message: string;
          redirectUri: string;
        }>({
          url: '/users/stop-impersonate',
          withApi: true,
        });

        if (response.redirectUri) {
          window.location.href = response.redirectUri;
        }
      },
      getGpo: () => {
        return get().user?.account?.gpo ?? null;
      },
      setUser: (user) => set({ user }),
      updateUser: (updateUserField) =>
        set((state) => ({ user: { ...state.user, ...updateUserField } })),
    })),
  ),
);
