import { VendorType } from '@/types';

export interface ConnectVendorParams {
  vendorId: string;
  username: string;
  password: string;
}

export interface ConnectAmazonVendorParams {
  vendorId: string;
  buyingGroupId: string;
}

export interface State {
  vendors: VendorType[];
  isLoading: boolean;
}

export interface Actions {
  getVendors: () => Promise<void>;
  connectVendor: (params: ConnectVendorParams) => Promise<void>;
  connectAmazonVendor: (
    params: ConnectAmazonVendorParams,
  ) => Promise<{ redirectUri: string }>;
}
