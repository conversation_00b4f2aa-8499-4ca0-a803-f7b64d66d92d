import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { get, post } from '@/libs/utils/api';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { createStore } from '@/utils';

import {
  Actions,
  ConnectVendorParams,
  ConnectAmazonVendorParams,
  State,
} from './types';
import { VendorType } from '@/types';

export const INITIAL_STATE: State = {
  vendors: [],
  isLoading: false,
};

export const useVendorsStore = createStore<State & Actions>()(
  immer(
    devtools((set) => ({
      ...INITIAL_STATE,
      getVendors: async () => {
        const activeClinic = useAccountStore.getState().activeClinic;

        const clinicId = activeClinic?.id;

        if (!clinicId) {
          return;
        }

        set({ isLoading: true });

        const response = await get<VendorType[]>({
          url: `/clinics/${clinicId}/vendors`,
        });

        set({ isLoading: false });
        set({ vendors: response });
      },
      connectVendor: async (params: ConnectVendorParams) => {
        let clinicId: string | undefined = '';

        const activeClinic = useAccountStore.getState().activeClinic;

        clinicId = activeClinic?.id;

        const updatedVendors = await post<VendorType[]>({
          url: `/clinics/${clinicId}/vendors`,
          body: {
            vendorId: params.vendorId,
            credentials: {
              username: params.username,
              password: params.password,
            },
          },
        });

        await useClinicStore.getState().getClinic(clinicId);

        set({ vendors: updatedVendors });
      },
      connectAmazonVendor: async (params: ConnectAmazonVendorParams) => {
        const { redirectUri } = await post<{
          redirectUri: string;
          status: string;
        }>({
          url: `/integrations/${params.vendorId}/connect`,
          body: {
            buyingGroupId: params.buyingGroupId,
          },
        });

        return { redirectUri };
      },
    })),
  ),
);
