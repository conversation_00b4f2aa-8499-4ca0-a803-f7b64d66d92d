import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface ModalOptionProps {
  name: string;
  [key: string]: unknown;
}

export type State = {
  modalOption: ModalOptionProps;
};

type Actions = {
  openModal: (modalOption: ModalOptionProps) => void;
  updateModalOption: (
    modalOption: Partial<ModalOptionProps>,
    isFullReplace?: boolean,
  ) => void;
  closeModal: () => void;
};

const INITIAL_VALUE = { name: '' };

export const useModalStore = create<State & Actions>()(
  immer(
    devtools((set, getState) => ({
      modalOption: INITIAL_VALUE,
      openModal: (modalOption: ModalOptionProps) => set({ modalOption }),
      updateModalOption: (option: Partial<ModalOptionProps>, isFullReplace) => {
        const { modalOption } = getState();

        if (isFullReplace) {
          set({
            modalOption: {
              name: modalOption.name,
              ...option,
            },
          });

          return;
        }

        set({
          modalOption: {
            ...modalOption,
            ...option,
          },
        });
      },
      closeModal: () => set({ modalOption: INITIAL_VALUE }),
    })),
  ),
);
