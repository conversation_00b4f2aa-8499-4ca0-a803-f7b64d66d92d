import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createStore } from '@/utils';

interface State {
  isBoxLoading: boolean;
  boxId: string;
}

interface Actions {
  setIsBoxLoading: (data: { isBoxLoading: boolean; boxId?: string }) => void;
  toggleBoxLoading: (boxId: string, fn: () => void) => void;
}

export const useBoxLoading = createStore<State & Actions>()(
  immer(
    devtools((set) => ({
      isBoxLoading: false,
      boxId: '',
      setIsBoxLoading: ({ boxId = '', isBoxLoading }) =>
        set({ isBoxLoading, boxId }),
      toggleBoxLoading: async (boxId, fn) => {
        set({
          boxId,
          isBoxLoading: true,
        });

        await fn();

        set({
          boxId: '',
          isBoxLoading: false,
        });
      },
    })),
  ),
);
