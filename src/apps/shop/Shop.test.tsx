import { render } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Shop } from './Shop';
import { MantineProvider } from '@mantine/core';

vi.mock('react-router-dom', async () => {
  const actual =
    await vi.importActual<typeof import('react-router-dom')>(
      'react-router-dom',
    );
  return {
    ...actual,
    RouterProvider: ({ children }: { children: React.ReactNode }) => (
      <div>{children}</div>
    ),
  };
});

const mockUseBoxLoading = vi.fn();
vi.mock('@/apps/shop/stores/useBoxLoadingStore', () => ({
  useBoxLoading: () => mockUseBoxLoading(),
}));

vi.mock('@/providers/ThemeProvider', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <MantineProvider>{children}</MantineProvider>
  ),
}));

describe('Shop component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    mockUseBoxLoading.mockReturnValue({
      isBoxLoading: false,
      boxId: 'mock-box-id',
    });

    render(<Shop />);
    expect(document.body).toBeInTheDocument();
  });

  it('does not show LoadingOverlay when isBoxLoading is false', () => {
    mockUseBoxLoading.mockReturnValue({
      isBoxLoading: false,
      boxId: 'mock-box-id',
    });

    render(<Shop />);
    const loadingOverlay = document.querySelector(
      '[data-testid="loading-overlay"]',
    );
    expect(loadingOverlay).toBeNull();
  });

  it('shows LoadingOverlay when isBoxLoading is true', () => {
    mockUseBoxLoading.mockReturnValue({
      isBoxLoading: true,
      boxId: 'mock-box-id',
    });

    render(<Shop />);
    const portal = document.querySelector('[data-portal]');
    expect(portal).toBeInTheDocument();
  });
});
