import { lazy, LazyExoticComponent } from 'react';
import { createBrowserRouter } from 'react-router-dom';
import { AuthLayout } from '@/libs/auth/components/AuthLayout/AuthLayout';
import { DashboardLayout } from '../Layouts/DashboardLayout/DashboardLayout';
import { GPO_ROUTES_PATH } from './routes';

const pages = [
  'Login',
  'ForgotPassword',
  'ChangePassword',
  'Dashboard',
  'SpendAnalysis',
] as const;
type PageName = (typeof pages)[number];

const pageModules: Record<
  PageName,
  LazyExoticComponent<() => JSX.Element>
> = pages.reduce(
  (acc, pageName) => ({
    ...acc,
    [pageName]: lazy(() =>
      import(`@/apps/gpo-portal/pages/${pageName}/${pageName}.tsx`).then(
        (pageModule) => ({
          default: pageModule[pageName] as React.FC,
        }),
      ),
    ),
  }),
  {} as Record<PageName, LazyExoticComponent<() => JSX.Element>>,
);

const { Login, ForgotPassword, ChangePassword, Dashboard, SpendAnalysis } =
  pageModules;

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthLayout
        bg="linear-gradient(180deg, #7a8ba8 0%, #1a3a6b 100%)"
        innerBg="#fff"
      />
    ),
    children: [
      { index: true, element: <Login /> },
      { path: GPO_ROUTES_PATH.login, element: <Login /> },
      { path: GPO_ROUTES_PATH.forgotPassword, element: <ForgotPassword /> },
      { path: GPO_ROUTES_PATH.changePassword, element: <ChangePassword /> },
    ],
  },
  {
    path: GPO_ROUTES_PATH.dashboard,
    element: <DashboardLayout />,
    children: [
      { index: true, element: <Dashboard /> },
      { path: GPO_ROUTES_PATH.spendAnalysis, element: <SpendAnalysis /> },
    ],
  },
]);
