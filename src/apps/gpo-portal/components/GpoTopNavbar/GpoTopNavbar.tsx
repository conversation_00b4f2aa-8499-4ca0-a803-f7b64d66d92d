import { Flex, Text } from '@mantine/core';
// import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { TopNavbar } from '@/libs/ui/TopNavbar/TopNavbar';

export const GpoTopNavbar = () => {
  // const { user } = useAuthStore();

  // TODO: Uncomment when API is ready
  // if (!user) {
  //   return null;
  // }

  return (
    <TopNavbar>
      <Flex gap="1rem" align="center">
        <Text fw="600" size="lg">
          GPO Portal
        </Text>
      </Flex>
    </TopNavbar>
  );
};
