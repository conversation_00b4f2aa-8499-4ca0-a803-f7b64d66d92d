import React from 'react';
import { DashboardCard } from '@/libs/dashboard';

interface PlatformUsageRateCardProps {
  className?: string;
}

interface UsageData {
  totalClinics: number;
  activeClinics: number;
  inactiveClinics: number;
  activePercentage: number;
  inactivePercentage: number;
}

const mockData: UsageData = {
  totalClinics: 7500,
  activeClinics: 5000,
  inactiveClinics: 2500,
  activePercentage: 66.67,
  inactivePercentage: 33.33,
};

export const PlatformUsageRateCard: React.FC<PlatformUsageRateCardProps> = ({
  className,
}) => {
  const data = mockData;

  const handleCtaClick = () => {
    // TODO: Implement download functionality
    console.log('Downloading platform usage report...');
  };

  const handleMoreOptions = () => {
    // TODO: Implement more options menu
    console.log('More options clicked');
  };

  return (
    <DashboardCard
      className={className}
      title="Platform Usage Rate"
      percentage={data.activePercentage}
      percentageLabel="OF THE CLINICS ARE ACTIVE"
      metrics={[
        {
          label: 'GPO Clinics',
          value: data.totalClinics.toLocaleString(),
        },
        {
          label: 'HF Active Clinics',
          value: data.activeClinics.toLocaleString(),
        },
      ]}
      progressData={{
        activePercentage: data.activePercentage,
        activeLabel: 'HF Active Clinics',
        activeValue: data.activeClinics.toLocaleString(),
        inactivePercentage: data.inactivePercentage,
        inactiveLabel: 'Inactive Clinics',
        inactiveValue: data.inactiveClinics.toLocaleString(),
      }}
      ctaText="Download Platform Usage Report"
      onCtaClick={handleCtaClick}
      onMoreOptions={handleMoreOptions}
    />
  );
};
