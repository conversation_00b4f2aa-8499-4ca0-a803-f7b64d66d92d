import React from 'react';
import { DashboardCard } from '@/libs/dashboard';
import { GPO_ROUTES_PATH } from '../../routes/routes';
import { useNavigate } from 'react-router-dom';

interface SpendAnalysisCardProps {
  className?: string;
}

interface SpendData {
  totalSpend: number;
  preferredSpend: number;
  nonPreferredSpend: number;
  preferredPercentage: number;
  nonPreferredPercentage: number;
}

const mockData: SpendData = {
  totalSpend: 100900000,
  preferredSpend: 20180000,
  nonPreferredSpend: 80720000,
  preferredPercentage: 20,
  nonPreferredPercentage: 80,
};

export const SpendAnalysisCard: React.FC<SpendAnalysisCardProps> = ({
  className,
}) => {
  const data = mockData;
  const navigate = useNavigate();

  const handleCtaClick = () => {
    navigate(GPO_ROUTES_PATH.spendAnalysis);
  };

  const handleMoreOptions = () => {
    // TODO: Implement more options menu
    console.log('More options clicked');
  };

  return (
    <DashboardCard
      className={className}
      title="Spend Analysis"
      percentage={data.preferredPercentage}
      percentageLabel="SPEND ON PREFERRED VENDORS"
      metrics={[
        {
          label: 'Total',
          value: `$${data.totalSpend.toLocaleString()}`,
        },
        {
          label: 'Preferred Vendors',
          value: `$${data.preferredSpend.toLocaleString()}`,
        },
      ]}
      progressData={{
        activePercentage: data.preferredPercentage,
        activeLabel: 'Preferred',
        activeValue: `$${data.preferredSpend.toLocaleString()}`,
        inactivePercentage: data.nonPreferredPercentage,
        inactiveLabel: 'Non-Preferred',
        inactiveValue: `$${data.nonPreferredSpend.toLocaleString()}`,
      }}
      ctaText="Detailed Spend Report"
      onCtaClick={handleCtaClick}
      onMoreOptions={handleMoreOptions}
    />
  );
};
