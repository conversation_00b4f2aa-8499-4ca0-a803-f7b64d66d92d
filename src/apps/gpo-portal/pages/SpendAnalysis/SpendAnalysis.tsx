import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { ClinicCard } from './components/ClinicCard/ClinicCard';
import { FilterControls } from './components/FilterControls/FilterControls';
import { useState, useMemo } from 'react';
import { AddressType } from '@/types/common';

export const SpendAnalysis = () => {
  const [selectedClinics, setSelectedClinics] = useState<Set<string>>(
    new Set(),
  );
  const [searchTerm, setSearchTerm] = useState('');
  const [clinicsPerPage, setClinicsPerPage] = useState(50);
  const [inactiveClinicsOnly, setInactiveClinicsOnly] = useState(false);

  const clinics = useMemo(
    () => [
      {
        clinic: {
          id: '1',
          name: 'Veterinary Care Center',
          ein: '12-3456789',
          email: '<EMAIL>',
          phoneNumber: '(*************',
          isActive: true,
          memberSince: '04/14/2022',
          shippingAddress: {
            addressId: 'addr1',
            street: '123 Main Street',
            state: 'CA',
            city: 'Los Angeles',
            postalCode: '90210',
          } as AddressType,
          practiceType: 'Mixed Animal',
          fulltimeDvmCount: 8,
          examRoomsCount: 10,
        },
        spendAnalysis: {
          totalSpend: '$10M',
          rebateEarned: '$75K',
          preferredShare: '75%',
          totalSpendYoy: 1,
          rebateEarnedYoy: 1,
          preferredShareYoy: 1,
          spendPercentage: '90%',
          spendAmount: '$ 184,590',
          spendYoyPercentage: 1,
          marketSharePercentage: '80%',
          vendorCount: '8 out of 10',
          marketShareYoyPercentage: 1,
          // Additional spend panel data
          annualBudget: '$12,067,650.00',
          prevYearSpend: '$11,493,000.64',
          preferredVendorsPercentage: 90,
          nonPreferredVendorsPercentage: 10,
          // Additional market share panel data
          distributorData: [
            { name: 'MWI', percentage: 60, color: '#4A90E2' },
            { name: 'Dist. A', percentage: 25, color: '#F5A623' },
            { name: 'Dist. B', percentage: 15, color: '#7ED321' },
          ],
          vendorData: [
            { name: 'Vendor A', percentage: 45, color: '#9B59B6' },
            { name: 'Vendor B', percentage: 30, color: '#E67E22' },
            { name: 'Vendor C', percentage: 25, color: '#3498DB' },
          ],
          productCategoriesData: {
            parasiticides: [
              { name: 'Brand X', percentage: 70, color: '#E74C3C' },
              { name: 'Brand Y', percentage: 20, color: '#2ECC71' },
              { name: 'Brand Z', percentage: 10, color: '#F39C12' },
            ],
            vaccines: [
              { name: 'Vaccine A', percentage: 55, color: '#8E44AD' },
              { name: 'Vaccine B', percentage: 35, color: '#16A085' },
              { name: 'Vaccine C', percentage: 10, color: '#D35400' },
            ],
            diets: [
              { name: 'Diet A', percentage: 40, color: '#27AE60' },
              { name: 'Diet B', percentage: 35, color: '#E67E22' },
              { name: 'Diet C', percentage: 25, color: '#2980B9' },
            ],
          },
        },
      },
      {
        clinic: {
          id: '2',
          name: 'Pet Wellness Clinic',
          ein: '98-7654321',
          email: '<EMAIL>',
          phoneNumber: '(*************',
          isActive: true,
          memberSince: '06/20/2021',
          shippingAddress: {
            addressId: 'addr2',
            street: '456 Oak Avenue',
            state: 'NY',
            city: 'New York',
            postalCode: '10001',
          } as AddressType,
          practiceType: 'Feline',
          fulltimeDvmCount: 5,
          examRoomsCount: 6,
        },
        spendAnalysis: {
          totalSpend: '$7.5M',
          rebateEarned: '$52K',
          preferredShare: '68%',
          totalSpendYoy: -0.5,
          rebateEarnedYoy: 0.8,
          preferredShareYoy: 0.3,
          spendPercentage: '85%',
          spendAmount: '$ 142,300',
          spendYoyPercentage: -0.2,
          marketSharePercentage: '72%',
          vendorCount: '6 out of 8',
          marketShareYoyPercentage: 0.5,
          // Additional spend panel data
          annualBudget: '$8,500,000.00',
          prevYearSpend: '$7,200,000.00',
          preferredVendorsPercentage: 85,
          nonPreferredVendorsPercentage: 15,
          // Additional market share panel data
          distributorData: [
            { name: 'MWI', percentage: 50, color: '#4A90E2' },
            { name: 'Dist. A', percentage: 30, color: '#F5A623' },
            { name: 'Dist. B', percentage: 20, color: '#7ED321' },
          ],
          vendorData: [
            { name: 'Vendor A', percentage: 40, color: '#9B59B6' },
            { name: 'Vendor B', percentage: 35, color: '#E67E22' },
            { name: 'Vendor C', percentage: 25, color: '#3498DB' },
          ],
          productCategoriesData: {
            parasiticides: [
              { name: 'Brand X', percentage: 60, color: '#E74C3C' },
              { name: 'Brand Y', percentage: 25, color: '#2ECC71' },
              { name: 'Brand Z', percentage: 15, color: '#F39C12' },
            ],
            vaccines: [
              { name: 'Vaccine A', percentage: 50, color: '#8E44AD' },
              { name: 'Vaccine B', percentage: 30, color: '#16A085' },
              { name: 'Vaccine C', percentage: 20, color: '#D35400' },
            ],
            diets: [
              { name: 'Diet A', percentage: 35, color: '#27AE60' },
              { name: 'Diet B', percentage: 40, color: '#E67E22' },
              { name: 'Diet C', percentage: 25, color: '#2980B9' },
            ],
          },
        },
      },
      {
        clinic: {
          id: '3',
          name: 'Emergency Animal Hospital',
          ein: '55-1234567',
          email: '<EMAIL>',
          phoneNumber: '(*************',
          isActive: false,
          memberSince: '09/15/2023',
          shippingAddress: {
            addressId: 'addr3',
            street: '789 Emergency Lane',
            state: 'TX',
            city: 'Houston',
            postalCode: '77001',
          } as AddressType,
          practiceType: 'Emergency',
          fulltimeDvmCount: 12,
          examRoomsCount: 15,
        },
        spendAnalysis: {
          totalSpend: '$15M',
          rebateEarned: '$120K',
          preferredShare: '82%',
          totalSpendYoy: 1.2,
          rebateEarnedYoy: 1.5,
          preferredShareYoy: 0.8,
          spendPercentage: '95%',
          spendAmount: '$ 298,750',
          spendYoyPercentage: 1.8,
          marketSharePercentage: '88%',
          vendorCount: '10 out of 12',
          marketShareYoyPercentage: 1.2,
          // Additional spend panel data
          annualBudget: '$18,000,000.00',
          prevYearSpend: '$13,500,000.00',
          preferredVendorsPercentage: 95,
          nonPreferredVendorsPercentage: 5,
          // Additional market share panel data
          distributorData: [
            { name: 'MWI', percentage: 70, color: '#4A90E2' },
            { name: 'Dist. A', percentage: 20, color: '#F5A623' },
            { name: 'Dist. B', percentage: 10, color: '#7ED321' },
          ],
          vendorData: [
            { name: 'Vendor A', percentage: 50, color: '#9B59B6' },
            { name: 'Vendor B', percentage: 25, color: '#E67E22' },
            { name: 'Vendor C', percentage: 25, color: '#3498DB' },
          ],
          productCategoriesData: {
            parasiticides: [
              { name: 'Brand X', percentage: 80, color: '#E74C3C' },
              { name: 'Brand Y', percentage: 15, color: '#2ECC71' },
              { name: 'Brand Z', percentage: 5, color: '#F39C12' },
            ],
            vaccines: [
              { name: 'Vaccine A', percentage: 60, color: '#8E44AD' },
              { name: 'Vaccine B', percentage: 25, color: '#16A085' },
              { name: 'Vaccine C', percentage: 15, color: '#D35400' },
            ],
            diets: [
              { name: 'Diet A', percentage: 45, color: '#27AE60' },
              { name: 'Diet B', percentage: 30, color: '#E67E22' },
              { name: 'Diet C', percentage: 25, color: '#2980B9' },
            ],
          },
        },
      },
    ],
    [],
  );

  const filteredClinics = useMemo(() => {
    let filtered = clinics.filter((clinicData) =>
      clinicData.clinic.name.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    if (inactiveClinicsOnly) {
      filtered = filtered.filter((clinicData) => !clinicData.clinic.isActive);
    }

    return filtered;
  }, [clinics, searchTerm, inactiveClinicsOnly]);

  const allSelected =
    filteredClinics.length > 0 &&
    filteredClinics.every((clinicData) =>
      selectedClinics.has(clinicData.clinic.id),
    );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allClinicIds = new Set(
        filteredClinics.map((clinicData) => clinicData.clinic.id),
      );
      setSelectedClinics(allClinicIds);
    } else {
      setSelectedClinics(new Set());
    }
  };

  const handleSelectClinic = (clinicId: string, checked: boolean) => {
    const newSelected = new Set(selectedClinics);
    if (checked) {
      newSelected.add(clinicId);
    } else {
      newSelected.delete(clinicId);
    }
    setSelectedClinics(newSelected);
  };

  return (
    <>
      <h1 className="text-2xl font-medium">Spend Analysis Details</h1>
      <div className="mt-4 rounded border border-black/[0.04] bg-white p-6">
        <header className="mb-4 flex items-center justify-between">
          <h2 className="m-0 font-medium">
            Your Clinics ({filteredClinics.length})
          </h2>
          <div className="flex gap-3">
            <div className="min-w-[220px]">
              <Input
                placeholder="Search Clinics"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                rightSection={
                  <Icon name="magnifier" size="16px" color="#C6C6C6" />
                }
              />
            </div>
            <Button variant="white" p="0 1.5rem">
              <Icon name="sort" size="20px" />
              <span className="ml-1">Order by</span>
            </Button>
            <Button variant="white" p="0 1.5rem">
              <Icon name="settings" size="20px" />
              <span className="ml-1">Filter by</span>
            </Button>
            <Button variant="white" className="max-w-[56px]">
              <Icon name="download" size="16px" />
            </Button>
          </div>
        </header>
        <div className="rounded border border-black/[0.04] bg-[#F2F8FC] p-6">
          <div className="mb-4 flex items-center justify-between">
            <label className="flex gap-4 pl-4">
              <Checkbox
                checked={allSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
              <p className="m-0">Select all clinics</p>
            </label>

            <FilterControls
              clinicsPerPage={clinicsPerPage}
              onClinicsPerPageChange={setClinicsPerPage}
              inactiveClinicsOnly={inactiveClinicsOnly}
              onInactiveClinicsToggle={setInactiveClinicsOnly}
            />
          </div>
          <div className="flex flex-col gap-2">
            {filteredClinics.map((clinicData) => (
              <ClinicCard
                key={clinicData.clinic.id}
                clinic={clinicData.clinic}
                spendAnalysis={clinicData.spendAnalysis}
                isSelected={selectedClinics.has(clinicData.clinic.id)}
                onSelect={(checked: boolean) =>
                  handleSelectClinic(clinicData.clinic.id, checked)
                }
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
};
