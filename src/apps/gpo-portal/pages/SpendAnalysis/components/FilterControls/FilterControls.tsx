import React from 'react';
import { ClinicsPerPage } from './components/ClinicsPerPage/ClinicsPerPage';
import { InactiveClinicsToggle } from './components/InactiveClinicsToggle/InactiveClinicsToggle';

interface FilterControlsProps {
  clinicsPerPage: number;
  onClinicsPerPageChange: (value: number) => void;
  inactiveClinicsOnly: boolean;
  onInactiveClinicsToggle: (enabled: boolean) => void;
}

export const FilterControls = ({
  clinicsPerPage,
  onClinicsPerPageChange,
  inactiveClinicsOnly,
  onInactiveClinicsToggle,
}: FilterControlsProps) => {
  return (
    <div className="flex items-center justify-between px-4">
      <div className="flex items-center gap-6">
        <ClinicsPerPage
          selectedValue={clinicsPerPage}
          onValueChange={onClinicsPerPageChange}
        />
        <InactiveClinicsToggle
          isEnabled={inactiveClinicsOnly}
          onToggle={onInactiveClinicsToggle}
        />
      </div>
    </div>
  );
};
