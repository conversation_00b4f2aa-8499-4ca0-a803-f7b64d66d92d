import React from 'react';
import { Toggle } from '@/libs/ui/Toggle/Toggle';

interface InactiveClinicsToggleProps {
  isEnabled: boolean;
  onToggle: (enabled: boolean) => void;
}

export const InactiveClinicsToggle = ({
  isEnabled,
  onToggle,
}: InactiveClinicsToggleProps) => {
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm font-medium text-[#344054]">
        Inactive Clinics Only
      </span>
      <Toggle enabled={isEnabled} onToggle={onToggle} size="md" />
    </div>
  );
};
