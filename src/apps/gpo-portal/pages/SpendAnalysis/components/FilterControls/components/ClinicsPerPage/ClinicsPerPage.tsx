import React from 'react';
import clsx from 'clsx';
import { FEATURE_FLAGS } from '@/constants';

interface ClinicsPerPageProps {
  selectedValue: number;
  onValueChange: (value: number) => void;
}

export const ClinicsPerPage = ({
  selectedValue,
  onValueChange,
}: ClinicsPerPageProps) => {
  const options = [50, 100, 500, 1000];

  if (!FEATURE_FLAGS.SPEND_ANALYSIS_CLINICS_PER_PAGE) {
    return null;
  }

  return (
    <div className="flex items-center gap-3">
      <span className="text-sm font-medium text-[#344054]">
        Clinics per page
      </span>
      <div className="flex items-center rounded-lg bg-[#F8F9FA] p-1">
        {options.map((option) => (
          <button
            key={option}
            onClick={() => onValueChange(option)}
            className={clsx('px-3 py-1 text-sm transition-colors', {
              'rounded-md bg-white text-[#344054] shadow-sm ring-1 ring-[#388bff]':
                selectedValue === option,
              'text-[#6B7280] hover:text-[#344054]': selectedValue !== option,
            })}
          >
            <span className="font-medium">{option}</span>
          </button>
        ))}
      </div>
    </div>
  );
};
