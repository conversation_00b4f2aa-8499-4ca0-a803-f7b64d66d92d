import React from 'react';
import { ProgressBar } from '@/libs/dashboard';
import { ReportPanel } from '../../../ReportPanel/ReportPanel';

interface MarketSharePanelProps {
  marketSharePercentage: string;
  vendorCount: string;
  yoyPercentage: number;
  // Additional dynamic data
  distributorData: Array<{
    name: string;
    percentage: number;
    color: string;
  }>;
  vendorData: Array<{
    name: string;
    percentage: number;
    color: string;
  }>;
  productCategoriesData: {
    parasiticides: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
    vaccines: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
    diets: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
  };
}

export const MarketSharePanel = ({
  marketSharePercentage,
  vendorCount,
  yoyPercentage,
  distributorData,
  vendorData,
  productCategoriesData,
}: MarketSharePanelProps) => {
  return (
    <ReportPanel
      title="Market Share Analysis"
      label="Preferred Vendor Market Share % (YOY)"
      value={marketSharePercentage}
      subtitle={`(${vendorCount})`}
      percentage={yoyPercentage}
    >
      <div className="flex flex-col gap-2 px-4 py-3">
        <div className="flex gap-5 rounded bg-[#F2F2F2] px-4 py-6">
          <div className="w-[170px]">
            <p>Distributor</p>
          </div>
          <div className="w-[1px] bg-[#ddd]" />
          <div className="flex-1">
            <ProgressBar
              values={distributorData.map((item) => ({
                value: item.percentage,
                color: item.color,
                label: item.name,
              }))}
            />
          </div>
        </div>
        <div className="flex gap-5 rounded bg-[#F2F2F2] px-4 py-6">
          <div className="w-[170px]">
            <p>Vendors</p>
          </div>
          <div className="w-[1px] bg-[#ddd]" />
          <div className="flex-1">
            <ProgressBar
              values={vendorData.map((item) => ({
                value: item.percentage,
                color: item.color,
                label: item.name,
              }))}
            />
          </div>
        </div>
        <div className="flex gap-5 rounded bg-[#F2F2F2] px-4 py-6">
          <div className="w-[170px]">
            <p>Products Categories</p>
          </div>
          <div className="w-[1px] bg-[#ddd]" />
          <div className="flex flex-1 flex-col gap-2">
            <ProgressBar
              title="Parasiticides"
              values={productCategoriesData.parasiticides.map((item) => ({
                value: item.percentage,
                color: item.color,
                label: item.name,
              }))}
            />
            <div className="my-4 h-[1px] w-full bg-[#ddd]" />

            <ProgressBar
              title="Vaccines"
              values={productCategoriesData.vaccines.map((item) => ({
                value: item.percentage,
                color: item.color,
                label: item.name,
              }))}
            />
            <div className="my-4 h-[1px] w-full bg-[#ddd]" />

            <ProgressBar
              title="Diets"
              values={productCategoriesData.diets.map((item) => ({
                value: item.percentage,
                color: item.color,
                label: item.name,
              }))}
            />
          </div>
        </div>
      </div>
    </ReportPanel>
  );
};
