import React from 'react';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { PercentageDisplay } from '@/libs/ui/PercentageDisplay';

interface ReportPanelProps {
  title: string;
  label: string;
  value: string;
  subtitle: string;
  percentage: number;
  children?: React.ReactNode;
  className?: string;
  variant?: 'clean' | 'default';
}

export const ReportPanel = ({
  title,
  label,
  value,
  subtitle,
  percentage,
  children,
  className = 'bg-[#F8FBFD]',
  variant = 'clean',
}: ReportPanelProps) => {
  return (
    <CollapsiblePanel
      variant={variant}
      header={
        <div className="flex w-full items-center justify-between px-4 py-4 pr-15">
          <h5 className="text-sm font-medium text-[#344054]">{title}</h5>
          <div className="flex items-center gap-2 rounded border border-[rgba(56,139,255,0.15)] bg-[rgba(56,139,255,0.05)] px-5 py-[6px]">
            <p className="text-[10px] font-medium text-[#666]">{label}</p>
            <p className="text-[14px] font-medium text-[#333]">{value}</p>
            <p className="text-[10px] font-medium text-[#666]">{subtitle}</p>
            <PercentageDisplay
              percentage={percentage}
              size="md"
              className="ml-2"
            />
          </div>
        </div>
      }
      content={
        <>
          <div className="mx-auto w-[calc(100%-32px)] border-t border-[#E4E7EC]" />
          {children}
        </>
      }
      className={className}
    />
  );
};
