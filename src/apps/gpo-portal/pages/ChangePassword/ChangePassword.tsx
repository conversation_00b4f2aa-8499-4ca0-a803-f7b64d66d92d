import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { ChangePasswordForm } from '@/libs/auth/components/ChangePasswordForm/ChangePasswordForm';
import { post } from '@/libs/utils/api';
import { successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

export const ChangePassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const changePasswordApiFunc = async (values?: {
    password: string;
    confirmPassword: string;
  }) => {
    if (!values) return;

    await post({
      url: '/gpo/password-resets/confirm',
      body: values,
    });

    successNotification(t('gpo.changePassword.successMessage'));
  };

  return (
    <ChangePasswordForm
      apiFunc={changePasswordApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.login)}
      schema={SCHEMA}
      namespace="gpo.changePassword"
    />
  );
};
