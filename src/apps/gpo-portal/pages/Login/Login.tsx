import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { LoginForm } from '@/libs/auth/components/LoginForm/LoginForm';

import { SCHEMA } from './constants';

export const Login = () => {
  const navigate = useNavigate();

  const loginApiFunc = async () => {};

  return (
    <LoginForm
      apiFunc={loginApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.dashboard)}
      forgotPasswordPath={GPO_ROUTES_PATH.forgotPassword}
      schema={SCHEMA}
      namespace="gpo.login"
    />
  );
};
