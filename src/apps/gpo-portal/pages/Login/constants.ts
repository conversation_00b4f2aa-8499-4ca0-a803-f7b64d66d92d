import i18n from '@/apps/gpo-portal/i18n';
import * as Yup from 'yup';

export const SCHEMA = Yup.object().shape({
  email: Yup.string()
    .email(() => i18n.t('form.errorMessage.email', { path: 'Email' }))
    .required(() => i18n.t('form.errorMessage.required', { path: 'Email' })),
  password: Yup.string()
    .required(() => i18n.t('form.errorMessage.required', { path: 'Password' }))
    .min(8, ({ min }) =>
      i18n.t('form.errorMessage.min', { path: 'Password', min }),
    ),
  rememberMe: Yup.boolean().required(),
});
