import { ReactNode } from 'react';

export interface OverviewType {
  title: string;
  value: string;
  additionValue?: string;
  customIcon?: ReactNode;
}

export interface GpoTableType {
  id: string;
  gpo: {
    imgUrl: string;
    name: string;
  };
  members: number;
  totalSpend: string;
  savings: string;
  rank: number;
}

export interface GpoOverViewType {
  totalMembers: number;
  totalSpend: number;
  totalSavings: number;
  averageSavingsPercentage: number;
  activeGpos: number;
}
