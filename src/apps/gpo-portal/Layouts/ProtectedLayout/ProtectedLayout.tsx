import { PropsWithChildren, useEffect } from 'react';
import { Outlet } from 'react-router-dom';

import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { UserType } from '@/types/common';

export const ProtectedLayout = ({ children }: PropsWithChildren) => {
  const { setUser } = useAuthStore();

  useEffect(() => {
    // TODO: Uncomment when API is ready
    // const loadUserData = async () => {
    //   try {
    //     const userData = await get<UserType>({
    //       url: '/gpo/users/me',
    //     });
    //     setUser(userData);
    //     if (additionApiCall) {
    //       await additionApiCall(userData);
    //     }
    //   } catch (err) {
    //     const { data } = err as ApiErrorProps;
    //     if (data?.message) {
    //       apiErrorNotification(data.message);
    //     }
    //   } finally {
    //     setIsLoading(false);
    //   }
    // };
    // loadUserData();

    // Mock user for development
    setUser({
      id: '1',
      name: 'GPO Admin',
      email: '<EMAIL>',
      accountId: '1',
    } as UserType);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // TODO: Uncomment when API is ready
  // if (isLoading) {
  //   return <LoadingOverlay visible />;
  // }

  // if (!user) {
  //   return <Navigate to={ROUTERS_PATH.login} />;
  // }

  return children || <Outlet />;
};
