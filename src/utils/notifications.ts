import {
  notifications as MNotifications,
  NotificationData,
} from '@mantine/notifications';

import i18n from '@/apps/shop/i18n';

export const notifications = {
  error: (options: NotificationData) => {
    MNotifications.show({
      ...options,
      color: 'var(--mantine-color-red-4)',
    });
  },
  success: (options: NotificationData) => {
    MNotifications.show({
      ...options,
      color: 'var(--mantine-color-green-4)',
    });
  },
};
// TODO update error handling
export const successNotification = (message?: string) =>
  notifications.success({
    title: i18n.t('common.success'),
    message: message ?? i18n.t('common.defaultSuccessMessage'),
  });

export const errorNotification = (message?: string) =>
  notifications.error({
    title: i18n.t('common.error'),
    message: message ?? i18n.t(`apiErrors.general`),
  });

const createAPINotification = (errorCode?: string) => {
  const message = errorCode || '';

  if (!errorCode) {
    errorNotification();

    return;
  }

  notifications.error({
    title: i18n.t('common.error'),
    message,
  });
};

export const apiErrorNotification = (apiErrorMessage: string) => {
  createAPINotification(apiErrorMessage);
};
