import { FactoryPayload, MantineTheme } from '@mantine/core';

const INPUT_XS_STYLE = {
  '--input-height-xs': '2rem',
  '--input-height': 'var(--input-height-xs)',
  '--mantine-font-size-xs': '1rem',
  '--input-fz': 'var(--mantine-font-size-xs)',
};

const INPUT_MD_STYLE = {
  '--input-height-md': '2.5rem',
  '--input-size': '2.5rem',
  '--input-height': 'var(--input-height-md)',
  '--mantine-font-size-md': '1rem',
  '--input-fz': 'var(--mantine-font-size-md)',
  '--input-line-height':
    'calc(var(--input-height) - calc(var(--mantine-scale)))',
};

export const getInputVariables = (
  _: MantineTheme,
  props: FactoryPayload['props'],
) => {
  if (props.size === 'xs') {
    return {
      input: INPUT_XS_STYLE,
    };
  }

  if (props.size === 'md') {
    return {
      input: INPUT_MD_STYLE,
    };
  }

  return { input: {} };
};

export const getNumberInputVariables = (
  _: MantineTheme,
  props: FactoryPayload['props'],
) => {
  if (props.size === 'xs') {
    return {
      input: INPUT_XS_STYLE,
      controls: {},
    };
  }

  if (props.size === 'md') {
    return {
      input: INPUT_MD_STYLE,
      controls: {},
    };
  }

  return { controls: {} };
};

export const getButtonVariables = (
  _: MantineTheme,
  props: FactoryPayload['props'],
) => {
  const baseStyle =
    props.variant === 'transparent'
      ? {
          '--button-hover': 'transparent',
        }
      : props.variant === 'default'
        ? {
            '--button-hover': '#fbfbfb',
          }
        : {
            '--mantine-color-yellow-filled-hover':
              'var(--mantine-color-yellow-5)',
            '--button-hover': 'var(--mantine-color-yellow-filled-hover)',
          };

  if (props.size === 'xs') {
    return {
      root: {
        '--button-height-xs': '1.75rem',
        '--button-height': 'var(--button-height-xs)',
        '--mantine-font-size-xs': '1rem',
        '--input-fz': 'var(--mantine-font-size-xs)',
        ...baseStyle,
      },
    };
  }

  if (props.size === 'sm') {
    return {
      root: {
        '--button-height-xs': '2rem',
        '--button-height': 'var(--button-height-xs)',
        '--mantine-font-size-xs': '0.875rem',
        '--input-fz': 'var(--mantine-font-size-xs)',
        ...baseStyle,
      },
    };
  }

  if (props.size === 'md') {
    return {
      root: {
        '--button-height-md': '2.5rem',
        '--button-height': 'var(--button-height-md)',
        '--mantine-font-size-md': '1rem',
        '--input-fz': 'var(--mantine-font-size-md)',
        ...baseStyle,
      },
    };
  }

  return { root: {} };
};

export const getActionIconButtonVariables = (
  _: MantineTheme,
  props: FactoryPayload['props'],
) => {
  if (props.size === 'xs') {
    return {
      root: {
        '--ai-size-xs': '28px',
        '--ai-size': 'var(--ai-size-xs)',
        '--ai-radius': '50%',
      },
    };
  }

  if (props.size === 'md') {
    return {
      root: {
        '--ai-size-md': '42px',
        '--ai-size': 'var(--ai-size-md)',
        '--ai-radius': '50%',
      },
    };
  }

  return {
    root: {},
  };
};

export const getRadioVariables = (
  _: MantineTheme,
  props: FactoryPayload['props'],
) => {
  if (props.size === 'xs') {
    return {
      root: {},
      icon: {
        '--radio-icon-size-xs': '0.65rem',
        '--radio-icon-size': 'var(--radio-icon-size-xs)',
      },
    };
  }

  if (props.size === 'md') {
    return {
      root: {},
      icon: {
        '--radio-icon-size-md': '1rem',
        '--radio-icon-size': 'var(--radio-icon-size-md)',
      },
    };
  }

  return { root: {} };
};

export const getSelectVariables = (
  _: MantineTheme,
  props: FactoryPayload['props'],
) => {
  if (props.size === 'xs') {
    return {
      input: INPUT_XS_STYLE,
      option: {
        '--combobox-option-fz': 'var(--mantine-font-size-md)',
        '--combobox-option-padding': 'var(--combobox-option-padding-md)',
      },
    };
  }

  if (props.size === 'md') {
    return {
      input: INPUT_MD_STYLE,
    };
  }

  return { input: {} };
};
