interface PriceConfig {
  currency?: string;
  formatter: Intl.NumberFormat;
}

const CURRENCY = {
  USD: 'USD',
};

const defaultPriceConfig = {
  currency: CURRENCY.USD,
  formatter: new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: CURRENCY.USD,
  }),
};

export const getPriceString = (
  price: number | string | null = 0,
  config: PriceConfig = defaultPriceConfig,
): string => {
  const { currency, formatter } = config;

  switch (currency) {
    case CURRENCY.USD:
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: CURRENCY.USD,
      }).format(Number(price));
    default:
      return formatter.format(Number(price));
  }
};
