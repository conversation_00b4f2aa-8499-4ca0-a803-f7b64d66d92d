import React from 'react';
import { RouterProvider, createMemoryRouter } from 'react-router-dom';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { render as testingLibraryRender } from '@testing-library/react';

export function render(ui: React.ReactNode) {
  return testingLibraryRender(<>{ui}</>, {
    wrapper: ThemeProvider,
  });
}

interface TestRouter {
  path: string;
  element: React.ReactNode;
}

export function renderWithNavigation(
  children: React.ReactNode,
  routes: TestRouter[] = [],
) {
  const options = { element: children, path: '/' };

  const router = createMemoryRouter([{ ...options }, ...routes], {
    initialEntries: [options.path],
    initialIndex: 1,
  });

  return render(<RouterProvider router={router} />);
}
