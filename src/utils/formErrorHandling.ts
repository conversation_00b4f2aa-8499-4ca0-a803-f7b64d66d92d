import { FieldErrors, FieldError } from 'react-hook-form';

import {
  ErrorValidationProps,
  ErrorValidationRootType,
  ErrorValidationType,
  ApiErrorMessagesProps,
  ApiErrorProps,
} from '@/types/utility';
import { errorNotification } from '@/utils/notifications';

type SetError<T> = (name: keyof T, error: FieldError) => void;

// for fields that don't have ref
export function setFormError<T>(errors: FieldErrors, setError: SetError<T>) {
  // TODO check nesting
  const errorValues = Object.values(errors) as ErrorValidationProps<T>[];

  if (errorValues.length > 0) {
    errorValues.forEach((error) => {
      if ((error as ErrorValidationRootType<T>).root) {
        if ((error as ErrorValidationRootType<T>).root.ref) {
          return;
        }

        const fieldError = Object.values(error) as ErrorValidationType<T>[];

        fieldError.forEach((fieldError) => {
          setError(fieldError.type, {
            type: 'custom',
            message: fieldError.message,
          });
        });
      }
    });
  }
}

export function setServerErrorForm<T>(
  errors: ApiErrorMessagesProps,
  setError: SetError<T>,
) {
  if (Object.values(errors).length) {
    Object.entries(errors).forEach(([field, message]) => {
      // const fieldDisplayName = i18n.t(`form.field.${field}`);

      setError(field as keyof T, {
        type: 'custom',
        message: message[0],
        // message: i18n.t(`apiErrors.${message}`, {
        //   field: fieldDisplayName.trim() === 'undefined' ? field : fieldDisplayName
        // })
      });
    });
  } else {
    errorNotification();
  }
}

export function defaultFormErrorHandler<T>(
  { apiResponse, data }: ApiErrorProps,
  setError: SetError<T>,
) {
  const { errors, message } = data ?? {};

  if (apiResponse?.status === 422) {
    setServerErrorForm(errors, setError);

    return;
  }

  errorNotification(message);
}
