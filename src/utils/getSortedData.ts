import { SORT_ORDERS } from '@/constants';
import { SortOptionsProps } from '@/types/utility';

export function getSortedData<T>(data: T[], sortOptions: SortOptionsProps<T>) {
  let sortValue = 0;

  return data.toSorted((a, b) => {
    const valueA = a[sortOptions.sortBy];
    const valueB = b[sortOptions.sortBy];

    if (typeof valueA === 'string' && typeof valueB === 'string') {
      sortValue = valueA.localeCompare(valueB);
    } else if (typeof valueA === 'number' && typeof valueB === 'number') {
      sortValue = valueA - valueB;
    } else {
      return 0;
    }

    return sortOptions.sortOrder === SORT_ORDERS.desc ? -sortValue : sortValue;
  });
}
