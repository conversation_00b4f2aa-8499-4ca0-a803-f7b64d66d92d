import { isStringAValidDate } from './date';

describe('Utils', () => {
  describe('Date', () => {
    describe('isStringAValidDate', () => {
      it('returns false when the string is not a valid date', () => {
        expect(isStringAValidDate('a')).toBeFalsy();
        expect(isStringAValidDate('31/31/31')).toBeFalsy();
      });

      it('returns true when the string is a valid date', () => {
        expect(isStringAValidDate('1990-06-19')).toBeTruthy();
      });
    });
  });
});
