import { ReactNode } from 'react';
import styles from './RecomendedWrap.module.css';

export type RecomendedWrapProps = {
  className?: string;
  children: ReactNode;
  label: string;
};

export const RecomendedWrap = ({
  className,
  children,
  label,
}: RecomendedWrapProps) => {
  return (
    <div className={`${className} ${styles.gpoList}`}>
      <span className={styles.gpoRecommendedTag}>{label}</span>
      {children}
    </div>
  );
};
