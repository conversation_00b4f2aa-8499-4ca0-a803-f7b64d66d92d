.container {
  border-radius: 0.5rem;
  border: 1px solid #f2f2f2;
  padding: 1rem;
  padding-bottom: 1.4rem;
  background-color: #fff;
  position: relative;

  h3 {
    font-size: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.5rem;
  }

  p {
    margin: 0;
  }
}

.imageContainer {
  border-radius: 0.5rem;
  border: 1px solid #f2f2f2;
  position: relative;
  width: 100%;
  height: 0;
  padding: 0 4rem 60%;
  overflow: hidden;

  img {
    position: absolute;
    width: 70%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    object-fit: scale-down;
  }
}

.specialInstructionsWrap {
  position: absolute;
  right: 1rem;
  bottom: 1rem;
}

.infoWrap {
  min-height: 200px;

  a {
    text-decoration: none;
    color: #333;
  }

  a:hover {
    text-decoration: underline;
  }
}

.vendorInfo {
  display: flex;
  margin: 0.5rem 0;
  font-size: 0.75rem;

  p span {
    color: rgba(102, 102, 102, 0.8);
  }

  p:first-child {
    padding-right: 0.75rem;
  }

  p:nth-child(2) {
    padding-left: 0.75rem;
    border-left: 1px solid #c7c7c7;
  }
}

.vendorImageWrapper {
  /* width: 120px; */
  position: relative;
  overflow: hidden;
}

.favoriteButtonWrapper {
  position: absolute;
  top: 1rem;
  right: 1rem;
}
