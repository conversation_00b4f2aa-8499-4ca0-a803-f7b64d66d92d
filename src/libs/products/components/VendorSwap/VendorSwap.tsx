import { Image, Menu, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useMemo } from 'react';
import ArrowDown from './assets/arrow-down.svg?react';
import { ProductType } from '@/types';
import { getPriceString } from '@/utils';
import { StockStatusIcon } from '../StockStatusIcon/StockStatusIcon';
import styles from './VendorSwap.module.css';

type VendorSwapProps = {
  currentOfferId: string;
  offers: ProductType['offers'];
  onSwap: (productOfferId: string) => void;
};

export const VendorSwap = ({
  offers,
  currentOfferId,
  onSwap,
}: VendorSwapProps) => {
  const handleSelectSwapVendor = (value: string | null) => {
    if (value && value !== currentOfferId) {
      onSwap(value);
    }
  };

  const swapOptions = useMemo(
    () =>
      offers.map(
        ({ id, vendor, price, clinicPrice, stockStatus, increments }) => ({
          value: id,
          label: vendor.name,
          imageUrl: vendor.imageUrl,
          price: clinicPrice || price,
          stockStatus,
          increments,
        }),
      ),
    [offers],
  );

  const { vendor, stockStatus } =
    offers.find(({ id }) => id === currentOfferId) || {};

  if (!vendor) {
    return null;
  }

  return (
    <Menu
      shadow="md"
      disabled={!swapOptions.length}
      position="bottom-start"
      offset={2}
    >
      <Menu.Target>
        <Flex
          mr="auto"
          py="0.5rem"
          pl="0.75rem"
          pr="0.375rem"
          align="center"
          className={styles.container}
        >
          <button className={styles.swapVendorButton}>
            <Image
              src={vendor.imageUrl}
              alt={vendor.name}
              h={24}
              title={vendor.name}
              mr="0.5rem"
            />
            <div className="mr-3">
              <StockStatusIcon status={stockStatus!} />
            </div>

            {swapOptions.length > 1 ? <ArrowDown /> : null}
          </button>
        </Flex>
      </Menu.Target>
      <Menu.Dropdown miw="120px" lang="0" px="0.25rem">
        {swapOptions.map(
          ({ label, value, stockStatus, price, imageUrl, increments }) => (
            <Menu.Item
              key={value}
              px="0.5rem"
              onClick={() => handleSelectSwapVendor(value)}
              bg={value === currentOfferId ? '#f1f3f5' : undefined}
            >
              <Flex justify="space-between" align="center" gap="2rem">
                <Flex align="center" gap="0.5rem">
                  <Image src={imageUrl} alt={label} h={24} title={label} />
                  <StockStatusIcon status={stockStatus} />
                </Flex>
                <Flex align="flex-end" gap="0.25rem">
                  <Text size="10px" fw="500" c="rgba(102, 102, 102, 0.8)">
                    {increments > 1 ? `Min. incr. ${increments}` : ''}
                  </Text>
                  <Text size="1rem" fw="500">
                    {getPriceString(price)}
                  </Text>
                </Flex>
              </Flex>
            </Menu.Item>
          ),
        )}
      </Menu.Dropdown>
    </Menu>
  );
};
