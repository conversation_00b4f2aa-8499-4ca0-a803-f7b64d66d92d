import { Divider, Rating, Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

export type ProductReviewsList = {
  reviews: {
    author: string;
    rating: number;
    content: string;
  }[];
};

export const ProductReviewsList = ({ reviews }: ProductReviewsList) => {
  return (
    <Flex direction="column">
      {reviews.map(({ author, content, rating }, index) => (
        <div key={+index} className="mb-3">
          <Rating defaultValue={rating} />
          <Title my="0.5rem" order={5}>
            {author}
          </Title>
          <Text mb="0.75rem">{content}</Text>
          {reviews.length - 1 > index && <Divider />}
        </div>
      ))}
    </Flex>
  );
};
