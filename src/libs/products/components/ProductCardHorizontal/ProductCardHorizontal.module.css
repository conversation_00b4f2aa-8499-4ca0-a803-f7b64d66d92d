.container {
  border-radius: 0.5rem;
  padding: 1rem;
  position: relative;
  display: flex;
  width: 100%;
}

.tag {
  border-radius: 0 0.25rem 0.25rem 0;
  background: rgba(54, 70, 172, 0.7);
  box-shadow: 4px 4px 0px 0px
    var(--Badges-Primary-drop-shadow-opacity, rgba(255, 255, 255, 0));
  position: absolute;
  top: 0.75rem;
  left: 0;
  color: #fff;
  padding: 0.25rem 0.5rem 0.25rem 0.75rem;
  font-weight: 500;
  z-index: 1;
}

.imageContainer {
  border-radius: 0.5rem;
  background-color: #fff;
  border: 1px solid #f2f2f2;
  position: relative;
  width: 240px;
  height: 130px;
  padding: 0 4rem 100%;
  overflow: hidden;
  padding: 0;

  img {
    position: absolute;
    width: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
    top: calc(50% + 1rem);
    max-width: calc(100% - 2rem);
    max-height: calc(100% - 2rem);
    object-fit: scale-down;
  }

  img[data-fallback='true'] {
    top: 50%;
  }
}
