.container {
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.75rem 1rem;
  border-radius: 0.25rem;
  box-shadow: 0 1px 4px 0 #0c0c0d0d;
}

.title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  font-size: 0.875rem;
}

.link {
  flex-shrink: 0;
  text-decoration: none;
  color: #333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &:hover {
    text-decoration: underline;
  }
}
