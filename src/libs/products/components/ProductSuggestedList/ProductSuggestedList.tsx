import { ProductType } from '@/types';
import { Flex } from '@/libs/ui/Flex/Flex';
import { SuggestedOfferItem } from '../SuggestedOfferList/SuggestedOfferItem';

export type ProductSuggestedListProps = {
  products: ProductType[];
};

export const ProductSuggestedList = ({
  products = [],
}: ProductSuggestedListProps) => {
  const offers = products.map((product) => product.offers[0]);
  return (
    <Flex direction="column">
      {offers.map((offer) => (
        <SuggestedOfferItem key={offer.id} offer={offer} />
      ))}
    </Flex>
  );
};
