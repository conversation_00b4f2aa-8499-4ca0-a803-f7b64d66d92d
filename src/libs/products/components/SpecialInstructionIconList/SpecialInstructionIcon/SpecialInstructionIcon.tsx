import { ReactElement } from 'react';
import {
  bioHazardIcon,
  pxIcon,
  snowFlakeIcon,
  pedigreeIcon,
  pillIcon,
} from './Icons';
import style from './SpecialInstructionIcon.module.css';
import { Tooltip } from '@mantine/core';
import { SpecialInstructionType } from '../types';

export type SpecialInstructionIconProps = {
  type: SpecialInstructionType;
};

type SpecialInstructionsData = Record<
  SpecialInstructionType,
  {
    image: ReactElement;
    color: string;
    title: string;
  }
>;
const SPECIAL_INSTRUCTIONS: SpecialInstructionsData = {
  isHazardous: {
    image: bioHazardIcon,
    color: '#FDDC98',
    title: 'Hazardous',
  },
  requiresColdShipping: {
    image: snowFlakeIcon,
    color: '#0072C6',
    title: 'Requires Cold Shipping',
  },
  requiresPrescription: {
    image: pxIcon,
    color: '#57ABB0',
    title: 'Requires Prescription',
  },
  isControlledSubstance: {
    image: pillIcon,
    color: '#A31838',
    title: 'Controller Substance',
  },
  requiresPedigree: {
    image: pedigreeIcon,
    color: '#A9913C',
    title: 'Requires Pedigree',
  },
};

export const SpecialInstructionIcon = ({
  type,
}: SpecialInstructionIconProps) => {
  const { color, image, title } = SPECIAL_INSTRUCTIONS[type];

  return (
    <Tooltip label={title}>
      <div className={style.container} style={{ background: color }}>
        {image}
      </div>
    </Tooltip>
  );
};
