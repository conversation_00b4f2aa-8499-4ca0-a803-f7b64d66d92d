export const snowFlakeIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clipPath="url(#clip0_13231_10864)">
      <path
        d="M14.646 8.99641C14.7573 9.27049 14.6254 9.58292 14.3514 9.69428L12.8628 10.2996L14.2405 11.1274C14.4974 11.2741 14.5867 11.6014 14.44 11.8583C14.2933 12.1152 13.9661 12.2045 13.7092 12.0578C13.7022 12.0539 13.6954 12.0498 13.6887 12.0455L12.3111 11.2178L12.4755 12.8169C12.501 13.1117 12.2826 13.3713 11.9879 13.3968C11.6998 13.4217 11.4439 13.2134 11.4098 12.9263L11.1634 10.5282L8.5308 8.94673V12.0178L10.4594 13.4643C10.6961 13.6418 10.7441 13.9776 10.5665 14.2143C10.389 14.451 10.0532 14.4989 9.81654 14.3214L8.53084 13.3571V14.9643C8.53084 15.2602 8.29099 15.5 7.99511 15.5C7.69923 15.5 7.45938 15.2602 7.45938 14.9643V13.3571L6.17368 14.3214C5.93699 14.4989 5.60119 14.451 5.42368 14.2143C5.24617 13.9776 5.29414 13.6418 5.53084 13.4643L7.45942 12.0178V8.94673L4.82697 10.5285L4.58054 12.9267C4.55251 13.1997 4.32266 13.4074 4.0482 13.4077C4.02978 13.4078 4.01136 13.4068 3.99302 13.4048C3.69889 13.3748 3.48478 13.1121 3.5148 12.818C3.51484 12.8178 3.51484 12.8176 3.51488 12.8173L3.67923 11.2182L2.30168 12.0459C2.05155 12.204 1.72067 12.1293 1.56266 11.8791C1.40462 11.629 1.4793 11.2981 1.72943 11.1401C1.73617 11.1359 1.74298 11.1318 1.74991 11.1278L3.12754 10.3L1.63902 9.69467C1.36673 9.57891 1.23984 9.26438 1.35556 8.99209C1.46871 8.72592 1.77269 8.59762 2.04233 8.70221L4.27551 9.61033L6.95515 7.99998L4.27528 6.38979L2.04209 7.29818C1.76626 7.40518 1.4559 7.26836 1.3489 6.99249C1.24428 6.72285 1.37261 6.41883 1.63878 6.30572L3.1273 5.70037L1.74964 4.8723C1.49269 4.72558 1.40337 4.39836 1.55009 4.14145C1.6968 3.88451 2.02403 3.79519 2.28093 3.9419C2.28786 3.94584 2.29468 3.94996 2.30141 3.95421L3.67896 4.78189L3.51461 3.18275C3.48443 2.88843 3.69854 2.62533 3.99287 2.59515C4.28719 2.56498 4.55029 2.77909 4.58046 3.07342L4.8269 5.47154L7.45938 7.05323V3.98212L5.5308 2.5357C5.29402 2.35826 5.2459 2.02251 5.42333 1.78573C5.60077 1.54896 5.93652 1.50083 6.17329 1.67827C6.17341 1.67835 6.17353 1.67846 6.17368 1.67854L7.45938 2.64281V1.03573C7.45938 0.739851 7.69923 0.5 7.99511 0.5C8.29099 0.5 8.53084 0.739851 8.53084 1.03573V2.64289L9.81654 1.67862C10.0532 1.50111 10.389 1.54908 10.5665 1.78577C10.7441 2.02247 10.6961 2.35826 10.4594 2.53578L8.5308 3.98212V7.05327L11.1632 5.47142L11.4097 3.0733C11.4446 2.77948 11.7111 2.56965 12.0049 2.60454C12.292 2.63865 12.5003 2.89454 12.4754 3.18263L12.311 4.78173L13.6886 3.95405C13.9387 3.79604 14.2696 3.87069 14.4276 4.12082C14.5856 4.37095 14.511 4.70183 14.2608 4.85984C14.2541 4.86408 14.2473 4.86821 14.2404 4.87214L12.8627 5.69994L14.3512 6.30529C14.6235 6.42105 14.7504 6.73558 14.6347 7.00787C14.5215 7.27404 14.2175 7.40234 13.9479 7.29775L11.7147 6.38963L9.03508 7.99998L11.7149 9.61018L13.9481 8.70205C14.2217 8.59046 14.534 8.72179 14.6456 8.9954C14.6457 8.99575 14.6458 8.99606 14.646 8.99641Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_13231_10864">
        <rect
          width="15"
          height="15"
          fill="white"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const pxIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="13"
    height="14"
    viewBox="0 0 13 14"
    fill="none"
  >
    <path
      d="M10.9382 7.77083H9.73815L8.52461 9.76458L6.86107 7.1901C8.29284 6.90521 9.37513 5.63958 9.37513 4.125C9.37513 2.40156 7.97357 1 6.25013 1H3.12305C2.26159 1 1.56055 1.70104 1.56055 2.5625V11.4167H2.60221V7.25H5.66315L7.93451 10.7333L6.25013 13.5H7.44961L8.5543 11.6849L9.73763 13.5H10.9371L9.14128 10.7203L10.9371 7.77083H10.9382ZM2.60273 2.5625C2.60273 2.275 2.83659 2.04167 3.12357 2.04167H6.25065C7.39961 2.04167 8.33398 2.97604 8.33398 4.125C8.33398 5.27396 7.39961 6.20833 6.25065 6.20833H2.60273V2.5625Z"
      fill="white"
      stroke="white"
      strokeWidth="0.3125"
    />
  </svg>
);

export const bioHazardIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clipPath="url(#clip0_13231_10873)">
      <path
        d="M8.00016 4.83594C7.28016 4.83594 6.62016 5.01594 6.05016 5.37594C6.20016 5.67594 6.41016 5.91594 6.68016 6.09594C7.07016 5.88594 7.52016 5.76594 8.00016 5.76594C8.48016 5.76594 8.93016 5.88594 9.32016 6.09594C9.56016 5.91594 9.77015 5.64594 9.95016 5.37594C9.38016 5.01594 8.72016 4.83594 8.00016 4.83594ZM11.8402 8.67594C11.5402 8.67594 11.2102 8.73594 10.9402 8.85594C10.8802 9.81594 10.3702 10.6559 9.62016 11.1359C9.65016 11.4359 9.77015 11.7659 9.95016 12.0359C11.1202 11.3759 11.9002 10.1159 11.9002 8.67594V8.64594H11.8402V8.67594ZM5.09016 8.85594C5.06016 8.85594 5.06016 8.85594 5.09016 8.85594C4.79016 8.73594 4.49016 8.67594 4.19016 8.67594H4.16016V8.70594C4.16016 10.1459 4.94016 11.4059 6.11016 12.0659C6.29016 11.7959 6.38016 11.4659 6.44016 11.1659C5.63016 10.6559 5.12016 9.81594 5.09016 8.85594Z"
        fill="black"
      />
      <path
        d="M14.8403 8.79547C14.4803 8.01547 13.7603 7.35547 12.9203 6.99547C12.4703 6.81547 11.9603 6.69547 11.4503 6.69547C11.6903 6.24547 11.8703 5.76547 11.9303 5.28547C12.0503 4.35547 11.8403 3.42547 11.3303 2.70547C10.8203 1.98547 10.0703 1.47547 9.2903 1.35547C9.9503 1.80547 10.4303 2.40547 10.6403 3.06547C10.8803 3.72547 10.8803 4.41547 10.7303 5.04547C10.5503 5.67547 10.1603 6.18547 9.6803 6.54547C9.2003 6.93547 8.6003 7.14547 8.0003 7.14547C7.4003 7.14547 6.8003 6.93547 6.3203 6.57547C5.8403 6.21547 5.4503 5.67547 5.2703 5.07547C5.0903 4.44547 5.0903 3.75547 5.3303 3.09547C5.5703 2.43547 6.0503 1.83547 6.6803 1.38547C5.9003 1.53547 5.1503 2.01547 4.6403 2.73547C4.1303 3.45547 3.9203 4.38547 4.0403 5.31547C4.1003 5.79547 4.2803 6.30547 4.5203 6.72547C4.0103 6.72547 3.5303 6.81547 3.0503 7.02547C2.2103 7.38547 1.4903 8.04547 1.1303 8.82547C0.770295 9.60547 0.710295 10.5055 0.980295 11.2855C1.0703 10.5055 1.3403 9.78547 1.7903 9.24547C2.2403 8.70547 2.8403 8.34547 3.4403 8.19547C4.0703 8.04547 4.7003 8.10547 5.2703 8.34547C5.8403 8.58547 6.2903 9.00547 6.5903 9.51547C6.8903 10.0255 7.0103 10.6555 6.9503 11.2555C6.8603 11.8555 6.5903 12.4555 6.1703 12.9055C5.7203 13.3555 5.1203 13.7155 4.4303 13.8355C3.7403 13.9855 2.9903 13.8355 2.2703 13.5055C2.7803 14.1055 3.5903 14.5255 4.4603 14.5855C5.3303 14.6755 6.2603 14.3755 6.9803 13.8355C7.3703 13.5355 7.7003 13.1455 7.9703 12.7255C8.2103 13.1755 8.5403 13.5355 8.9603 13.8355C9.6803 14.4055 10.6103 14.6755 11.4803 14.5855C12.3503 14.4955 13.1603 14.1055 13.6703 13.5055C12.9503 13.8355 12.2003 13.9555 11.5103 13.8355C10.8203 13.7155 10.2203 13.3855 9.7703 12.9055C9.3203 12.4255 9.0503 11.8555 8.9903 11.2555C8.9303 10.6555 9.0503 10.0255 9.3503 9.51547C9.6503 9.00547 10.1303 8.58547 10.6703 8.34547C11.2403 8.10547 11.8703 8.04547 12.5003 8.19547C13.1303 8.34547 13.7303 8.70547 14.1503 9.24547C14.6003 9.78547 14.8703 10.4755 14.9603 11.2855C15.2903 10.5055 15.2303 9.60547 14.8403 8.79547ZM8.3303 9.42547C8.2103 9.45547 8.1203 9.48547 8.0003 9.48547C7.8803 9.48547 7.7903 9.45547 7.6703 9.42547C7.4003 9.30547 7.2203 9.03547 7.2203 8.70547C7.2203 8.67547 7.2203 8.64547 7.2203 8.61547C7.2503 8.37547 7.3703 8.19547 7.5503 8.07547C7.6703 7.98547 7.8503 7.92547 8.0003 7.92547C8.1803 7.92547 8.3303 7.98547 8.4503 8.07547C8.6303 8.19547 8.7503 8.40547 8.7803 8.61547V8.70547C8.7803 9.03547 8.6003 9.30547 8.3303 9.42547Z"
        fill="black"
      />
    </g>
    <defs>
      <clipPath id="clip0_13231_10873">
        <rect
          width="15"
          height="15"
          fill="white"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const pillIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M13.124 8.0958L13.9181 7.30171C15.3606 5.85925 15.3607 3.5245 13.9181 2.0819C12.4757 0.639462 10.1409 0.639271 8.69833 2.0819C8.39044 2.38974 2.36117 8.41906 2.08191 8.69832C0.639445 10.1408 0.639281 12.4755 2.08191 13.9181C3.52432 15.3606 5.85909 15.3608 7.3017 13.9181L8.09581 13.124C8.73063 14.2427 9.93268 14.9992 11.3082 14.9992C13.3434 14.9992 14.9992 13.3434 14.9992 11.3082C14.9992 9.93267 14.2427 8.73061 13.124 8.0958ZM7.61724 11.3082C7.61724 11.6537 7.66509 11.9882 7.75426 12.3056L6.72173 13.3381C5.59985 14.46 3.78392 14.4602 2.6619 13.3381C1.54001 12.2163 1.53985 10.4003 2.6619 9.27831L5.65508 6.28513L8.40346 9.03347C7.91113 9.66077 7.61724 10.4508 7.61724 11.3082ZM10.8981 14.1495C9.50872 13.9499 8.43744 12.7519 8.43744 11.3082C8.43744 9.86448 9.50872 8.66647 10.8981 8.46686V14.1495ZM8.97798 8.44807L6.23504 5.70514L9.27829 2.66189C10.4002 1.54003 12.2161 1.53986 13.3381 2.66189C14.46 3.78375 14.4602 5.5997 13.3381 6.72172L12.3056 7.75425C11.9882 7.66506 11.6537 7.61723 11.3082 7.61723C10.4253 7.61723 9.61388 7.92903 8.97798 8.44807ZM11.7183 14.1495V8.46688C13.1077 8.66649 14.1789 9.8645 14.1789 11.3082C14.1789 12.7519 13.1077 13.9499 11.7183 14.1495Z"
      fill="white"
      stroke="white"
      strokeWidth="0.25"
    />
  </svg>
);

export const pedigreeIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clipPath="url(#clip0_13976_13799)">
      <path
        d="M6.90743 12.4885C6.53012 12.6083 6.11985 12.6019 5.743 12.4648C5.36615 12.3276 5.04789 12.0688 4.83583 11.7346C4.44021 11.7181 4.05695 11.5717 3.74979 11.314C3.71099 11.2815 3.67369 11.2474 3.6381 11.2119L2.65826 13.2625C2.58467 13.4164 2.60561 13.5988 2.71204 13.7321C2.81847 13.8654 2.99174 13.9262 3.15825 13.8884L5.12227 13.443L6.00953 15.251C6.08437 15.4034 6.23944 15.5 6.40904 15.5H6.41145C6.58208 15.4991 6.73714 15.4007 6.81061 15.2467L7.94964 12.8632C7.57016 12.8505 7.20532 12.7181 6.90743 12.4885Z"
        fill="white"
      />
      <path
        d="M12.2697 11.314C11.9625 11.5717 11.5791 11.718 11.1836 11.7344C10.9716 12.0688 10.6532 12.3276 10.2764 12.4647C9.89961 12.6019 9.48934 12.6083 9.11203 12.4885C8.81414 12.7181 8.44931 12.8505 8.06982 12.8632L9.20874 15.2467C9.28232 15.4007 9.43739 15.499 9.60802 15.4999H9.61031C9.78002 15.4999 9.93497 15.4034 10.0098 15.251L10.8972 13.443L12.8612 13.8883C13.0276 13.9262 13.2009 13.8654 13.3073 13.7321C13.4138 13.5988 13.4347 13.4164 13.3612 13.2625L12.3813 11.2119C12.3457 11.2474 12.3085 11.2814 12.2697 11.314Z"
        fill="white"
      />
      <path
        d="M13.4795 5.96327C13.639 5.759 13.7047 5.49567 13.6598 5.24092C13.6148 4.98618 13.4631 4.76119 13.2434 4.62375C13.0702 4.51549 12.9929 4.30331 13.056 4.10899C13.136 3.86249 13.1078 3.59264 12.9783 3.36856C12.849 3.14449 12.6294 2.98507 12.3759 2.93106C12.1762 2.8886 12.031 2.71557 12.0238 2.51152C12.0146 2.25243 11.8957 2.00855 11.6976 1.84227C11.4994 1.67588 11.2386 1.60126 10.9818 1.63719C10.7797 1.66569 10.584 1.55262 10.5074 1.36334C10.4103 1.12301 10.2152 0.934416 9.97198 0.845953C9.72891 0.757491 9.45814 0.776488 9.22937 0.898138C9.04902 0.993925 8.82654 0.954672 8.68979 0.802924C8.51641 0.610435 8.26853 0.5 8.00978 0.5C7.75103 0.5 7.50316 0.610435 7.32978 0.802924C7.19302 0.954672 6.97055 0.993925 6.79019 0.898138C6.56142 0.776488 6.29066 0.757491 6.04759 0.845953C5.8044 0.934416 5.60928 1.12301 5.51212 1.36334C5.43556 1.55262 5.23998 1.66569 5.03776 1.63719C4.78107 1.60126 4.52015 1.67588 4.32194 1.84227C4.12373 2.00855 4.00494 2.25243 3.99578 2.51152C3.98857 2.71557 3.84346 2.8886 3.64365 2.93117C3.39017 2.98507 3.17055 3.14449 3.04124 3.36856C2.9118 3.59264 2.88354 3.86249 2.96353 4.10899C3.02659 4.30331 2.94934 4.51549 2.77619 4.62386C2.55647 4.76119 2.40472 4.98618 2.35974 5.24092C2.31488 5.49578 2.38057 5.759 2.5401 5.96339C2.66576 6.12429 2.66576 6.3502 2.5401 6.5111C2.38057 6.71538 2.31488 6.9787 2.35974 7.23345C2.40472 7.48831 2.55647 7.71318 2.77619 7.85062C2.94934 7.95889 3.0267 8.17106 2.96365 8.36538C2.88354 8.61188 2.91192 8.88173 3.04124 9.10581C3.17067 9.32988 3.39017 9.4893 3.64365 9.54331C3.84346 9.58577 3.98857 9.7588 3.99578 9.96297C4.00494 10.2219 4.12384 10.4659 4.32205 10.6322C4.52026 10.7985 4.78119 10.8732 5.03776 10.8372C5.23998 10.8089 5.43556 10.9217 5.51212 11.1111C5.60928 11.3514 5.80452 11.54 6.04759 11.6284C6.29077 11.7169 6.56142 11.6979 6.79031 11.5763C6.85622 11.5412 6.92798 11.5243 6.99893 11.5243C7.12195 11.5243 7.24303 11.5752 7.32978 11.6714C7.50327 11.8641 7.75115 11.9744 8.00978 11.9744C8.26853 11.9744 8.51641 11.8639 8.6899 11.6714C8.82654 11.5197 9.04902 11.4806 9.22937 11.5763C9.45826 11.698 9.72891 11.7169 9.97209 11.6284C10.2152 11.54 10.4103 11.3514 10.5074 11.1111C10.584 10.9217 10.7797 10.8088 10.9819 10.8372C11.2385 10.8731 11.4994 10.7985 11.6976 10.6322C11.8958 10.4659 12.0147 10.2219 12.0239 9.96297C12.031 9.7588 12.1762 9.58589 12.3759 9.54331C12.6294 9.4893 12.849 9.32988 12.9784 9.10581C13.1078 8.88173 13.136 8.61188 13.056 8.36538C12.993 8.17117 13.0702 7.95889 13.2434 7.85062C13.4632 7.71318 13.615 7.48831 13.6599 7.23356C13.7048 6.9787 13.6391 6.71549 13.4796 6.5111C13.3538 6.35008 13.3538 6.12429 13.4795 5.96327ZM8.00978 10.1368C5.85956 10.1368 4.11011 8.38747 4.11011 6.23724C4.11011 4.08691 5.85956 2.33757 8.00978 2.33757C10.16 2.33757 11.9093 4.08691 11.9093 6.23724C11.9093 8.38747 10.16 10.1368 8.00978 10.1368Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_13976_13799">
        <rect
          width="15"
          height="15"
          fill="white"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
