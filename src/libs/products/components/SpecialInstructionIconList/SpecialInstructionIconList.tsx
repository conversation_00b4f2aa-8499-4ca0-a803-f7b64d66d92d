import { Flex } from '@/libs/ui/Flex/Flex';
import { SpecialInstructionIcon } from './SpecialInstructionIcon/SpecialInstructionIcon';
import { SpecialInstructionType } from './types';
import styles from './SpecialInstructionIconList.module.css';

type SpecialInstructionIconListProps = Record<SpecialInstructionType, boolean>;

export const SpecialInstructionIconList = ({
  isControlledSubstance,
  isHazardous,
  requiresColdShipping,
  requiresPedigree,
  requiresPrescription,
}: SpecialInstructionIconListProps) => {
  const specialInstructionsObj = {
    isControlledSubstance,
    isHazardous,
    requiresColdShipping,
    requiresPedigree,
    requiresPrescription,
  };

  const specialInstructions = Object.keys(specialInstructionsObj).filter(
    (key): key is SpecialInstructionType =>
      Boolean(
        specialInstructionsObj[key as keyof SpecialInstructionIconListProps],
      ),
  );

  return (
    <Flex gap="0.2rem" className={styles.container}>
      {specialInstructions.map((instruction) => (
        <SpecialInstructionIcon key={instruction} type={instruction} />
      ))}
    </Flex>
  );
};
