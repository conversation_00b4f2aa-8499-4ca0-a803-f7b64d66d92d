import type { Meta, StoryObj } from '@storybook/react';
import { AddToCartButton } from './AddToCartButton';

const meta: Meta<typeof AddToCartButton> = {
  title: 'Product/AddToCartButton',
  component: AddToCartButton,
};
export default meta;

type Story = StoryObj<typeof AddToCartButton>;

export const Default: Story = {
  args: {},
};
export const QuantityInCart: Story = {
  args: {
    quantityInCart: 4,
  },
};
