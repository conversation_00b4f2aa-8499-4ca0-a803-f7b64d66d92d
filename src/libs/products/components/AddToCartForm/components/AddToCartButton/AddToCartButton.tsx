import { Text } from '@mantine/core';
import { Button, ButtonBaseProps } from '@/libs/ui/Button/Button';
import CartSummaryIcon from '@/assets/images/cart/cart-summary-small.svg?react';
import styles from './AddToCartButton.module.css';
import { useTranslation } from 'react-i18next';

export type AddToCartButtonProps = {
  isLoading: boolean;
  quantityInCart?: number;
  isDisabled?: boolean;
  fullWidth?: boolean;
  onlyIcon?: boolean;
  onClick?: () => void;
  size?: ButtonBaseProps['size'];
};

export const AddToCartButton = ({
  isLoading,
  quantityInCart,
  isDisabled = false,
  onlyIcon = false,
  size = 'md',
  onClick,
}: AddToCartButtonProps) => {
  const { t } = useTranslation();

  const hasItemsInCart = Boolean(quantityInCart);

  const renderIconOnlyContent = () => {
    if (hasItemsInCart)
      return (
        <>
          <Text className={styles.shortText} lh={1}>
            +
          </Text>
          <div className={`${styles.container} bg-[#BDDCF0] px-2`}>
            <CartSummaryIcon />
            <Text lh={1}>{quantityInCart}</Text>
          </div>
        </>
      );

    return (
      <div className={`${styles.container} bg-[#FADF82] px-2`}>
        <CartSummaryIcon />
        <Text lh={1}>+</Text>
      </div>
    );
  };

  const renderFullButtonContent = () => {
    if (hasItemsInCart)
      return (
        <>
          <Text className={styles.shortText}>{`+ ${t('common.add')}`}</Text>
          <div className={`px-0 ${styles.container} bg-[#BDDCF0]`}>
            <CartSummaryIcon />
            <Text lh={1}>{quantityInCart}</Text>
          </div>
        </>
      );

    return <Text>{t('client.search.addToCart')}</Text>;
  };

  const buttonContent = onlyIcon
    ? renderIconOnlyContent()
    : renderFullButtonContent();

  return (
    <Button
      disabled={isDisabled}
      loading={isLoading}
      size={size}
      onClick={onClick}
      p="0"
    >
      {buttonContent}
    </Button>
  );
};
