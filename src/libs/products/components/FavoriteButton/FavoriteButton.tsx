import { Button } from '@/components';
import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import ProductFavoriteIcon from '@/assets/images/product/favorite.svg?react';
import ProductFavoriteFilledIcon from '@/assets/images/product/favorite-filled.svg?react';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { useEffect, useState } from 'react';
type FavoriteButtonProps = {
  productId: string;
  isFavorite: boolean;
};

export const FavoriteButton = ({
  productId,
  isFavorite: isFavoriteProp,
}: FavoriteButtonProps) => {
  const [isFavorite, setIsFavorite] = useState(isFavoriteProp);
  const { addToFavorite, removeToFavorite } = useProductStore();

  const { apiRequest: handleToggleFavorite, isLoading: isFavoriteLoading } =
    useAsyncRequest({
      apiFunc: async () => {
        if (isFavorite) {
          const itWasRemoved = await removeToFavorite(productId);
          setIsFavorite(!itWasRemoved);
        } else {
          const itWasAdded = await addToFavorite(productId);
          setIsFavorite(itWasAdded);
        }
      },
    });

  useEffect(() => {
    setIsFavorite(isFavoriteProp);
  }, [isFavoriteProp]);

  return (
    <Button
      loading={isFavoriteLoading}
      onClick={handleToggleFavorite}
      variant="transparent"
      size="sm"
      p="2"
      iconOnly
    >
      {isFavorite ? <ProductFavoriteFilledIcon /> : <ProductFavoriteIcon />}
    </Button>
  );
};
