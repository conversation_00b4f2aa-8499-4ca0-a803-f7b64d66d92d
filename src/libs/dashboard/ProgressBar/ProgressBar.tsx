interface ProgressValue {
  value: number;
  color: string;
  label?: string;
}

interface ProgressBarProps {
  values: ProgressValue[];
  title?: string;
  showLegend?: boolean;
  height?: string;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  values,
  title,
  showLegend = true,
  height = 'h-[10px]',
  className = '',
}) => {
  const totalValue = values.reduce((sum, value) => sum + value.value, 0);

  return (
    <div className={`${className}`}>
      {title && <p className="mb-1 text-xs font-medium text-[#666]">{title}</p>}

      <div
        className={`mb-4 flex ${height} items-center justify-center overflow-hidden border border-[#D7D7D7] bg-[#F9F4FF] px-[1px]`}
      >
        {values.map((value, index) => {
          const percentage =
            totalValue > 0 ? (value.value / totalValue) * 100 : 0;

          return (
            <div
              key={index}
              className={`h-[6px] transition-all duration-300 ${value.color.startsWith('bg-') ? value.color : ''}`}
              style={{
                width: `${percentage}%`,
                ...(value.color.startsWith('bg-')
                  ? {}
                  : { backgroundColor: value.color }),
              }}
              title={value.label}
            />
          );
        })}
      </div>

      {showLegend && values.length > 0 && (
        <div className="flex flex-wrap gap-4">
          {values.map((value, index) => {
            const percentage =
              totalValue > 0 ? (value.value / totalValue) * 100 : 0;

            return (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{
                    backgroundColor: value.color.startsWith('bg-')
                      ? undefined
                      : value.color,
                  }}
                />
                <span className="text-xs text-[#666]">
                  {value.label} {percentage.toFixed(0)}%
                </span>
                {index < values.length - 1 && (
                  <div className="mx-2 h-5 w-[1px] bg-[#ddd]" />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
