import React from 'react';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { Select } from '@/libs/form/Select';
import { PeriodOption, useTimePeriod } from '@/libs/utils/hooks/useTimePeriod';
import { ProgressBar } from '@/libs/dashboard/ProgressBar/ProgressBar';

interface DashboardCardProps {
  className?: string;
  title: string;
  percentage: number;
  percentageLabel: string;
  metrics: {
    label: string;
    value: string;
  }[];
  progressData: {
    activePercentage: number;
    activeLabel: string;
    activeValue: string;
    inactivePercentage: number;
    inactiveLabel: string;
    inactiveValue: string;
  };
  ctaText: string;
  onCtaClick: () => void;
  onMoreOptions?: () => void;
  timePeriodOptions?: string[];
  defaultTimePeriod?: string;
}

export const DashboardCard: React.FC<DashboardCardProps> = ({
  className,
  title,
  percentage,
  percentageLabel,
  metrics,
  progressData,
  ctaText,
  onCtaClick,
  onMoreOptions,
  timePeriodOptions = [
    'entire year',
    'last month',
    'last quarter',
    'last 6 months',
  ],
  defaultTimePeriod = 'entire year',
}) => {
  const { period, setPeriod, options, formattedRange } = useTimePeriod({
    defaultPeriod: defaultTimePeriod as PeriodOption,
    availableOptions: timePeriodOptions as PeriodOption[],
  });

  const timePeriods = options.map((option) => ({
    value: option,
    label:
      option.charAt(0).toUpperCase() +
      option.slice(1).replace(/([A-Z])/g, ' $1'),
  }));

  return (
    <div
      className={`flex flex-col rounded-lg border border-gray-200 bg-white px-[1.5rem] py-[2rem] ${className || ''}`}
    >
      <div className="mb-8 flex items-center justify-between border-b border-gray-200 pb-8">
        <h2 className="text-base font-medium text-gray-900">{title}</h2>
        <div className="flex w-1/2 items-center justify-end gap-3">
          <div className="w-[140px]">
            <Select
              value={period}
              onChange={(e) => setPeriod(e.target.value as PeriodOption)}
              options={timePeriods}
            />
          </div>

          <Button
            variant="white"
            className="max-w-[60px]"
            aria-label="Download"
            onClick={() => {
              console.log('Downloading detailed spend report...');
            }}
          >
            <Icon name="download" aria-hidden={true} />
          </Button>
          {onMoreOptions && (
            <Button
              variant="unstyled"
              onClick={onMoreOptions}
              aria-label="More options"
            >
              <Icon
                name="moreOptions"
                size="1rem"
                color="#333"
                aria-hidden={true}
              />
            </Button>
          )}
        </div>
      </div>

      <div className="mb-6 flex flex-1 items-start justify-between">
        <div>
          <p className="text-[2rem] leading-none font-bold text-[#344054]">
            {percentage}%
          </p>
          <div className="mt-1 text-sm tracking-wide text-gray-500 uppercase">
            {percentageLabel}
          </div>
        </div>
        <div className="mx-4 h-16 w-px bg-gray-200"></div>
        <div className="flex flex-col gap-2 text-right">
          {metrics.map((metric, index) => (
            <div key={index} className="text-sm text-[#666]">
              {metric.label}:{' '}
              <span className="font-bold text-[#333]">{metric.value}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-6 rounded-[.5rem] bg-[#F2F8FC] p-4">
        <ProgressBar
          showLegend={false}
          values={[
            {
              value: progressData.activePercentage,
              color: 'bg-blue-600',
              label: progressData.activeLabel,
            },
            {
              value: progressData.inactivePercentage,
              color: 'bg-yellow-400',
              label: progressData.inactiveLabel,
            },
          ]}
        />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-blue-600"></div>
            <span className="text-sm">
              {progressData.activeLabel} ({progressData.activeValue}){' '}
              {progressData.activePercentage}%
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
            <span className="text-sm">
              {progressData.inactiveLabel} ({progressData.inactiveValue}){' '}
              {progressData.inactivePercentage}%
            </span>
          </div>
        </div>

        <div className="my-4 h-[1px] w-full bg-gray-200"></div>

        <div className="mb-8 text-sm text-gray-500">
          Results filtered by:{' '}
          <span className="font-bold">
            {timePeriods.find((t) => t.value === period)?.label} (
            {formattedRange})
          </span>
        </div>
      </div>

      <Button
        variant="white"
        size="md"
        className="min-h-[3rem]"
        onClick={onCtaClick}
      >
        {ctaText}
      </Button>
    </div>
  );
};
