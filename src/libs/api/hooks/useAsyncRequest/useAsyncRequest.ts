import { ApiErrorProps } from '@/types/utility';
import { apiErrorNotification } from '@/utils';
import { useCallback, useState } from 'react';

export type useAsyncRequestFunc = useAsyncRequestProps['apiFunc'];

interface useAsyncRequestProps<P = unknown, R = unknown> {
  apiFunc: (params?: P) => Promise<void> | Promise<R> | R;
  errorFunc?: (err: ApiErrorProps) => void;
  successFunc?: () => void;
}

interface useLoadingReturnProps<P, R> {
  isLoading: boolean;
  hasError?: boolean;
  result?: R;
  apiRequest: (params?: P) => Promise<void> | void;
}

export const useAsyncRequest = <P, R = unknown>({
  apiFunc,
  errorFunc,
  successFunc,
}: useAsyncRequestProps<P, R>): useLoadingReturnProps<P, R> => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [result, setResult] = useState<R | undefined>(undefined);

  const apiRequest = useCallback(
    async (params?: P) => {
      try {
        setIsLoading(true);

        const result = await apiFunc(params);

        setResult(result as R);
        setIsLoading(false);
        // TODO: add successFunc to realizations
        successFunc?.();
      } catch (err) {
        const apiError = err as ApiErrorProps;

        setIsLoading(false);
        setHasError(true);

        if (errorFunc) {
          errorFunc(apiError);
        } else {
          const { message } = apiError.data ?? {};

          apiErrorNotification(message);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [apiFunc, errorFunc],
  );

  return {
    isLoading,
    hasError,
    result,
    apiRequest,
  };
};
