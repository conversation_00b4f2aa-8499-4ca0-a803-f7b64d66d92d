import {
  RecommendedTag,
  type RecommendedTagProps,
} from '@/libs/ui/RecommendedTag/RecommendedTag';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';

type GpoRecommendedTagProps = {
  isRecommended: boolean;
} & Omit<RecommendedTagProps, 'children'>;

export const GpoRecommendedTag = ({
  isRecommended,
  ...tagProp
}: GpoRecommendedTagProps) => {
  const { getGpo } = useAuthStore();

  const gpo = getGpo();

  if (!(gpo && isRecommended)) {
    return null;
  }

  return <RecommendedTag {...tagProp}>{gpo.name} recommended</RecommendedTag>;
};
