import { Text } from '@mantine/core';
import { getPriceString } from '@/utils';
import { Alert } from '@/libs/ui/Alert/Alert';
import DiscountIcon from './assets/discount.svg?react';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';

interface SavingAlertProps {
  value: number;
}
export const SavingAlert = ({ value }: SavingAlertProps) => {
  const { getGpo } = useAuthStore();
  const gpo = getGpo();

  return gpo && value ? (
    <Alert icon={<DiscountIcon />}>
      <Text>
        You are saving{' '}
        <Text span fw="500">
          {getPriceString(value)}
        </Text>{' '}
        by shopping{' '}
        <Text span fw="500">
          {gpo.name} recommendations
        </Text>
      </Text>
    </Alert>
  ) : null;
};
