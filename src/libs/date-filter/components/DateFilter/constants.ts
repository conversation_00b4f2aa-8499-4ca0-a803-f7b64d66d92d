import dayjs from 'dayjs';
import * as Yup from 'yup';

export const TODAY_DAYJS = dayjs();
export const PREDEFINED_DATES = {
  LAST_WEEK: {
    from: TODAY_DAYJS.subtract(1, 'week').startOf('week').toDate(),
    to: TODAY_DAYJS.subtract(1, 'week').endOf('week').toDate(),
  },
  LAST_6_MONTHS: {
    from: TODAY_DAYJS.subtract(6, 'month').toDate(),
    to: TODAY_DAYJS.toDate(),
  },
  LAST_12_MONTHS: {
    from: TODAY_DAYJS.subtract(1, 'year').toDate(),
    to: TODAY_DAYJS.toDate(),
  },
} as const;

export const SCHEMA = Yup.object().shape({
  from: Yup.string().notRequired(),
  to: Yup.string(),
});

const OLDEST_YEAR_OPTION = 2015;
const CURRENT_YEAR = TODAY_DAYJS.get('year');

export const SELECT_YEAR_OPTIONS = Array.from({
  length: CURRENT_YEAR - OLDEST_YEAR_OPTION + 1,
}).map((_, index) => ({
  label: (CURRENT_YEAR - index).toString(),
  value: (CURRENT_YEAR - index).toString(),
}));
