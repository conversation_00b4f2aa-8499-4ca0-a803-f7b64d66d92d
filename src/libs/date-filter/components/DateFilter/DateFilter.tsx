import { ReactNode, useEffect, useState } from 'react';
import { Controller, Resolver, useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import {
  Popover,
  Button as MantineButton,
  CloseButton,
  Text,
  Divider,
} from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { yupResolver } from '@hookform/resolvers/yup';
import SlidersIcon from '@/assets/images/product/sliders.svg?react';

import styles from './DateFilter.module.css';
import { DEFAULT_SERVER_DATE_FORMAT } from '@/constants';
import { DatePickerInput } from '@/libs/form/DatePickerInput';
import { Button } from '@/libs/ui/Button/Button';
import { Select } from '@/libs/form/Select';
import {
  PREDEFINED_DATES,
  SCHEMA,
  SELECT_YEAR_OPTIONS,
  TODAY_DAYJS,
} from './constants';
import { getEntireYearFromRange, getYearRange } from './utils';
import { Flex } from '@/libs/ui/Flex/Flex';

interface FormValues {
  from?: Date;
  to?: Date;
}

export interface DateFilterProps {
  onChange: (dates: { from: string; to: string }) => void;
  onClearFilters?: VoidFunction;
  applyedValues: {
    from: string;
    to: string;
  };
  icon?: ReactNode;
  label?: ReactNode;
}

export const DateFilter = ({
  applyedValues,
  onChange,
  onClearFilters,
  icon,
  label,
}: DateFilterProps) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPredefinedDate, setSelectedPredefinedDate] = useState<
    keyof typeof PREDEFINED_DATES | null
  >(null);

  const { control, handleSubmit, setValue, getValues, reset } =
    useForm<FormValues>({
      resolver: yupResolver(SCHEMA) as Resolver<FormValues>,
      defaultValues: {
        to: dayjs(applyedValues.to).toDate(),
        from: dayjs(applyedValues.from).toDate(),
      },
    });

  useEffect(() => {
    const { from, to } = getValues();

    const fromDate = dayjs(applyedValues.from).toDate();
    const toDate = dayjs(applyedValues.to).toDate();

    const fromChanged = !dayjs(from).isSame(fromDate, 'day');
    const toChanged = !dayjs(to).isSame(toDate, 'day');

    if (fromChanged || toChanged) {
      handleClearPredefinedDate();
    }

    if (fromChanged) {
      setValue('from', fromDate);
    }

    if (toChanged) {
      setValue('to', toDate);
    }
  }, [applyedValues, getValues, setValue]);

  const handleToggle = () => {
    setIsOpen((prevState) => !prevState);
  };

  const handleClearPredefinedDate = () => setSelectedPredefinedDate(null);

  const handleCalendUpdate =
    ({ onChange }: { onChange: (value: Date | null) => void }) =>
    (value: Date | null) => {
      handleClearPredefinedDate();
      onChange(value);
    };

  const handleSearch = handleSubmit((values) => {
    reset(values);
    onChange({
      from:
        (values.from &&
          dayjs(values.from).format(DEFAULT_SERVER_DATE_FORMAT)) ??
        '',
      to:
        (values.to && dayjs(values.to).format(DEFAULT_SERVER_DATE_FORMAT)) ??
        '',
    });

    setIsOpen(false);
    reset();
  });

  const getSetPredefinedDateHandler =
    (type: 'LAST_WEEK' | 'LAST_6_MONTHS' | 'LAST_12_MONTHS') => () => {
      const { from, to } = PREDEFINED_DATES[type];
      setSelectedPredefinedDate(type);
      setValue('from', from);
      setValue('to', to);
    };

  const setPreviousYearRange = (year: string) => {
    const { from, to } = getYearRange(year);
    setSelectedPredefinedDate(null);
    setValue('from', from);
    setValue('to', to);
  };

  const entireYearFromRange = getEntireYearFromRange(getValues());

  return (
    <Popover position="bottom-end" opened={isOpen}>
      <Popover.Target>
        <MantineButton
          onClick={() => {
            handleToggle();
          }}
          leftSection={icon ?? <SlidersIcon />}
          variant="default"
        >
          {label ? label : 'Filter by'}
        </MantineButton>
      </Popover.Target>

      <Popover.Dropdown w="600px" p="2rem">
        <Flex flex="row" justify="space-between" align="flex-start" mb="md">
          <Text size="1.25rem" fw="500" c="black">
            Filter
          </Text>
          <CloseButton onClick={() => setIsOpen(false)} />
        </Flex>
        <form onSubmit={handleSearch}>
          <div className={`mb-4 p-4 ${styles.box}`}>
            <Flex gap="1rem">
              <div className="flex-1">
                <Controller
                  name="from"
                  control={control}
                  render={({ field }) => (
                    <DatePickerInput
                      label="From:"
                      value={field.value}
                      onUpdate={handleCalendUpdate(field)}
                      maxDate={TODAY_DAYJS.toDate()}
                    />
                  )}
                />
              </div>
              <div className="flex-1">
                <Controller
                  name="to"
                  control={control}
                  render={({ field }) => (
                    <DatePickerInput
                      label="To:"
                      value={field.value}
                      onUpdate={handleCalendUpdate(field)}
                      maxDate={TODAY_DAYJS.toDate()}
                    />
                  )}
                />
              </div>
            </Flex>
            <Divider my="1rem" />
            <Text size="14px" c="344054" mb="1rem">
              Timeframe
            </Text>
            <Flex
              gap="1rem"
              className={
                selectedPredefinedDate
                  ? styles.selectedPredefinedDateContainer
                  : ''
              }
            >
              <Button
                onClick={getSetPredefinedDateHandler('LAST_WEEK')}
                className={
                  selectedPredefinedDate === 'LAST_WEEK'
                    ? styles.selectedPredefinedDate
                    : ''
                }
                variant="white"
                type="button"
              >
                {t('client.orderHistory.lastWeek')}{' '}
              </Button>
              <Button
                onClick={getSetPredefinedDateHandler('LAST_6_MONTHS')}
                className={
                  selectedPredefinedDate === 'LAST_6_MONTHS'
                    ? styles.selectedPredefinedDate
                    : ''
                }
                variant="white"
                type="button"
              >
                {t('client.orderHistory.6months')}
              </Button>
              <Button
                onClick={getSetPredefinedDateHandler('LAST_12_MONTHS')}
                className={
                  selectedPredefinedDate === 'LAST_12_MONTHS'
                    ? styles.selectedPredefinedDate
                    : ''
                }
                variant="white"
                type="button"
              >
                {t('client.orderHistory.year')}
              </Button>
            </Flex>
            <Divider my="1rem" />
            <div>
              <Select
                label="Select Previous Years:"
                options={SELECT_YEAR_OPTIONS}
                defaultValue={
                  entireYearFromRange ? +entireYearFromRange : undefined
                }
                key={entireYearFromRange}
                onChange={(event) => setPreviousYearRange(event.target.value)}
              />
            </div>
          </div>

          <Button type="submit" variant="secondary">
            {t('client.orderHistory.search')}
          </Button>

          {onClearFilters ? (
            <Button
              variant="unstyled"
              onClick={() => {
                onClearFilters();
                setIsOpen(false);
              }}
              style={{
                color: '#0072C6',
                fontSize: '1rem',
                fontWeight: '500',
                textAlign: 'center',
                width: '100%',
                marginTop: '1rem',
              }}
            >
              Clear Filters
            </Button>
          ) : null}
        </form>
      </Popover.Dropdown>
    </Popover>
  );
};
