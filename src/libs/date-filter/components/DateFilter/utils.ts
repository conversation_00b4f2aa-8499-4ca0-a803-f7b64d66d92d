import dayjs from 'dayjs';

export const getEntireYearFromRange = (input: {
  from?: Date;
  to?: Date;
}): number | undefined => {
  const inputFrom = dayjs(input.from);
  const inputTo = dayjs(input.to);

  const isEntireYear =
    inputFrom.date() === 1 &&
    inputFrom.month() === 0 &&
    inputTo.date() === 31 &&
    inputTo.month() === 11 &&
    inputFrom.year() === inputTo.year();

  return isEntireYear ? inputFrom.year() : undefined;
};
export const getYearRange = (year: number | string) => {
  const from = dayjs(`${year}-01-01`).startOf('day').toDate();
  const to = dayjs(`${year}-12-31`).endOf('day').toDate();

  return { from, to };
};
