import { forwardRef, InputHTMLAttributes, useId } from 'react';
import styles from './Input.module.css';

type InputProps = {
  id?: string;
  label?: string;
  error?: string;
  variant?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
} & InputHTMLAttributes<HTMLInputElement>;

export const Checkbox = forwardRef<HTMLInputElement, InputProps>(
  ({ id, label, error, align = 'left', onChange, ...rest }, ref) => {
    const generatedId = useId();
    const inputId = id || generatedId;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
    };

    return (
      <>
        <div className="relative flex items-center gap-2">
          <input
            id={inputId}
            ref={ref}
            onChange={handleChange}
            aria-invalid={!!error}
            style={{ textAlign: align }}
            type="checkbox"
            {...rest}
          />
          {label && (
            <label htmlFor={inputId} className={styles.label}>
              {label}
            </label>
          )}
        </div>
        {error && <p className={styles.error}>{error}</p>}
      </>
    );
  },
);

Checkbox.displayName = 'Checkbox';
