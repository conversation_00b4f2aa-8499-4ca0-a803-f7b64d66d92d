import { forwardRef, SelectHTMLAttributes } from 'react';
import styles from './Input.module.css';
import clsx from 'clsx';
import { Flex } from '@/libs/ui/Flex/Flex';
import { HelpTooltip } from '../ui/HelpTooltip/HelpTooltip';

type Option = {
  label: string;
  value: string;
};

type SelectProps = {
  id?: string;
  label?: string;
  error?: string;
  variant?: 'sm' | 'md' | 'lg';
  options: Option[];
  tooltip?: string;
} & SelectHTMLAttributes<HTMLSelectElement>;

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ id, label, error, variant = 'md', options, tooltip, ...rest }, ref) => {
    return (
      <div className={styles.container}>
        {label && (
          <Flex gap="0.5rem" align="center" mb="0.4rem">
            <label htmlFor={id} className={styles.label}>
              {label}
            </label>
            {tooltip && <HelpTooltip message={tooltip} />}
          </Flex>
        )}
        <select
          id={id}
          ref={ref}
          aria-invalid={!!error}
          className={clsx(styles.input, styles.select, {
            [styles.inputError]: error,
            [styles.inputMedium]: variant === 'md',
            [styles.inputLarge]: variant === 'lg',
          })}
          {...rest}
        >
          <option value="">Select an option</option>
          {options.map(({ label, value }) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
        {error && <p className={styles.error}>{error}</p>}
      </div>
    );
  },
);

Select.displayName = 'Select';
