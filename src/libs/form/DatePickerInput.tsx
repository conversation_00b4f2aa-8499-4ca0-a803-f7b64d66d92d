import { Popover } from '@mantine/core';
import { Input, type InputProps } from './Input';
import { DatePicker } from '@mantine/dates';
import { useState } from 'react';
import dayjs from 'dayjs';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import styles from './DatePickerInput.module.css';

type DatePickerInputProps = Omit<InputProps, 'value'> & {
  minDate?: Date;
  maxDate?: Date;
  value?: Date;
  onUpdate?: (val: Date | null) => void;
};

export const DatePickerInput = ({
  label,
  minDate,
  maxDate,
  onUpdate,
  value,
  ...props
}: DatePickerInputProps) => {
  const [opened, setOpened] = useState(false);

  return (
    <Popover withArrow opened={opened} onChange={setOpened}>
      <Popover.Target>
        <div>
          <Input
            {...props}
            label={label}
            value={dayjs(value).format(DEFAULT_DISPLAY_DATE_FORMAT)}
            onFocus={() => setOpened(true)}
            readOnly
          />
        </div>
      </Popover.Target>
      <Popover.Dropdown onClick={(event) => event.stopPropagation()}>
        <DatePicker
          value={dayjs(value).toDate()}
          defaultDate={dayjs(value).toDate()}
          minDate={minDate}
          maxDate={maxDate}
          className={styles.container}
          onChange={(val) => {
            if (val && onUpdate) {
              onUpdate(val);
            }
            setOpened(false);
          }}
        />
      </Popover.Dropdown>
    </Popover>
  );
};
