import { useState, useRef, useEffect, forwardRef, FocusEvent } from 'react';
import styles from './MultiSelect.module.css';
import { Checkbox } from './Checkbox';
import { Flex } from '@/libs/ui/Flex/Flex';
import { HelpTooltip } from '../ui/HelpTooltip/HelpTooltip';

type Option = {
  label: string;
  value: string;
};

type MultiSelectProps = {
  options: Option[];
  label?: string;
  placeholder?: string;
  name: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  value?: string[];
  defaultValue?: string[];
  tooltip?: string;
};

export const MultiSelect = forwardRef<HTMLInputElement, MultiSelectProps>(
  (
    {
      label,
      options,
      placeholder,
      name,
      onChange,
      onBlur,
      value: controlledValue,
      defaultValue = [],
      tooltip,
    },
    ref,
  ) => {
    const [open, setOpen] = useState(false);
    const [search, setSearch] = useState('');
    const [value, setValue] = useState<string[]>(defaultValue);
    const containerRef = useRef<HTMLDivElement>(null);

    const isControlled = controlledValue !== undefined;
    const currentValue = isControlled ? controlledValue : value;

    const filtered = options.filter((option) =>
      option.label.toLowerCase().includes(search.toLowerCase()),
    );

    const emitChange = (newValue: string[]) => {
      if (!isControlled) {
        setValue(newValue);
      }

      const event = {
        target: {
          name,
          value: newValue,
        },
      } as unknown as React.ChangeEvent<HTMLInputElement>;

      onChange?.(event);
    };

    const handleSelect = (val: string) => {
      const newValue = currentValue.includes(val)
        ? currentValue.filter((v) => v !== val)
        : [...currentValue, val];

      emitChange(newValue);
      setSearch('');
    };

    const handleRemove = (val: string) => {
      emitChange(currentValue.filter((v) => v !== val));
    };

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          containerRef.current &&
          !containerRef.current.contains(event.target as Node)
        ) {
          setOpen(false);
          onBlur?.(event as unknown as React.FocusEvent<HTMLInputElement>);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }, [onBlur]);

    return (
      <>
        <Flex gap="0.5rem" align="center" mb="0.4rem">
          {label && (
            <label htmlFor={name} className={styles.label}>
              {label}
            </label>
          )}
          {tooltip && <HelpTooltip message={tooltip} />}
        </Flex>
        <div className={styles.multiselect} ref={containerRef}>
          <div className={styles.inputWrapper} onClick={() => setOpen(true)}>
            {currentValue.map((val) => {
              const label = options.find((d) => d.value === val)?.label || val;
              return (
                <span key={val} className={styles.tag}>
                  {label}
                  <button
                    type="button"
                    className={styles.remove}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemove(val);
                    }}
                  >
                    ×
                  </button>
                </span>
              );
            })}
            <input
              id={name}
              className={styles.input}
              placeholder={currentValue.length === 0 ? placeholder : ''}
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
                setOpen(true);
              }}
            />
          </div>
          {open && (
            <div className={styles.dropdown}>
              {filtered.length === 0 ? (
                <div className={`${styles.option} ${styles.disabled}`}>
                  No options
                </div>
              ) : (
                filtered.map((opt) => (
                  <div
                    key={opt.value}
                    className={styles.option}
                    onClick={() => handleSelect(opt.value)}
                  >
                    <Checkbox checked={currentValue.includes(opt.value)} />
                    {opt.label}
                  </div>
                ))
              )}
            </div>
          )}

          <input
            type="hidden"
            name={name}
            value={JSON.stringify(currentValue)}
            ref={ref}
          />
        </div>
      </>
    );
  },
);

MultiSelect.displayName = 'MultiSelect';
