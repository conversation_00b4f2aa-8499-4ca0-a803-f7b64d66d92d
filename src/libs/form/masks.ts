export const einMask = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/^(\d{2})(\d)/, '$1-$2')
    .replace(/(-\d{7})\d+?$/, '$1');
};

export const postalCodeMask = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/^(\d{5})(\d)/, '$1-$2')
    .replace(/(-\d{4})\d+?$/, '$1');
};

export const phoneMask = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/^(\d{3})(\d)/, '($1) $2')
    .replace(/(\(\d{3}\)\s\d{3})(\d)/, '$1-$2')
    .replace(/(-\d{4})\d+?$/, '$1');
};

export const moneyMask = (value: string): string => {
  const numericValue = value.replace(/\D/g, '');
  const padded = numericValue.padStart(2, '0');
  const cents = padded.slice(-2);
  const whole = padded.slice(0, -2).replace(/^0+/, '') || '0';
  const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return `$${formattedWhole}.${cents}`;
};

export const integerMoneyMask = (value: string) => {
  const integerValue = +value.replace(/\D/g, '');

  return `$${integerValue.toLocaleString('en-US')}`;
};
