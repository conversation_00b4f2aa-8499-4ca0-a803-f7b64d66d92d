.multiselect {
  position: relative;
  width: 100%;
}

.label {
  display: block;
  font-size: 0.875rem;
}

.inputWrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: #fff;
  border: 1px solid #eaecf0;
  padding: 5px;
  min-height: 40px;
  cursor: text;
  border-radius: 4px;
}

.inputWrapper:focus-visible {
  outline: 2px solid #f9d763;
  outline-offset: 0.125rem;
}

.input {
  border: none;
  flex: 1;
  min-width: 80px;
  padding: 5px;
  font-size: 14px;
  outline: none;
}

.tag {
  background: #f2f8fc;
  border: 1px solid #e5f1f9;
  border-radius: 100px;
  color: #007bff;
  border-radius: 2px;
  padding: 4px 6px 4px 8px;
  margin: 2px;
  display: flex;
  align-items: center;
  font-size: 13px;
  border-radius: 1rem;
}

.tag button {
  font-size: 20px;
  line-height: 0.3;
  padding-right: 0;
}

.remove {
  background: none;
  border: none;
  margin-left: 4px;
  cursor: pointer;
  color: #007bff;
  font-size: 14px;
}

.dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  width: 100%;
  max-height: 160px;
  overflow-y: auto;
  border: 1px solid #ccc;
  background: white;
  z-index: 10;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.option {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  gap: 0.5rem;
}

.option:hover {
  background: #f2f2f2;
}

.option.disabled {
  color: #aaa;
  cursor: default;
}
