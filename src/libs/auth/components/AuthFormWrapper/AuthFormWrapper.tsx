import { ReactNode } from 'react';
import { Text } from '@mantine/core';

import styles from './AuthFormWrapper.module.css';

interface AuthFormWrapperProps {
  title: string;
  subtitle?: string | ReactNode;
  children: ReactNode;
  footer?: ReactNode;
}

export const AuthFormWrapper = ({
  title,
  subtitle,
  children,
  footer,
}: AuthFormWrapperProps) => {
  return (
    <>
      <Text ta="center" size="xlLg" className={styles.title}>
        {title}
      </Text>

      {subtitle && (
        <Text ta="center" size="md" className={styles.subtitle}>
          {subtitle}
        </Text>
      )}

      <div className={styles.formWrapper}>{children}</div>

      {footer && <div className={styles.footer}>{footer}</div>}
    </>
  );
};
