import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';

import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { AuthFormWrapper } from '../AuthFormWrapper/AuthFormWrapper';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler } from '@/utils';
import { ApiErrorProps } from '@/types/utility';
import styles from './ForgotPasswordForm.module.css';

interface ForgotPasswordFormProps {
  onSuccess: (userData: unknown) => void;
  onError?: (error: ApiErrorProps) => void;
  apiFunc: (values: FormValues) => Promise<unknown>;
  isLoading?: boolean;
  loginPath: string;
  schema: Yup.ObjectSchema<FormValues>;
  namespace?: string;
}

export interface FormValues {
  email: string;
}

export const ForgotPasswordForm = ({
  onSuccess,
  onError,
  apiFunc,
  isLoading: externalLoading = false,
  loginPath,
  schema,
  namespace = 'forgotPassword',
}: ForgotPasswordFormProps) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const { apiRequest: handleResetPassword, isLoading: internalLoading } =
    useAsyncRequest({
      apiFunc: async (values?: FormValues) => {
        if (!values) return;
        const result = await apiFunc(values);
        onSuccess(result);
      },
      errorFunc: (error) => {
        defaultFormErrorHandler(error, setError);
        onError?.(error);
      },
    });

  const isLoading = externalLoading || internalLoading;

  const handleResetPasswordSubmit = handleSubmit((values) => {
    handleResetPassword(values);
  });

  return (
    <AuthFormWrapper
      title={t(`${namespace}.title`)}
      subtitle={t(`${namespace}.subtitle`)}
      footer={
        <>
          {t(`${namespace}.haveAccount`)}
          <Link to={loginPath}>{t(`${namespace}.logIn`)}</Link>
        </>
      }
    >
      <form onSubmit={handleResetPasswordSubmit}>
        <Input
          label={t('form.field.email')}
          placeholder={t('form.field.email')}
          {...register('email')}
          disabled={isLoading}
          size="md"
          error={errors.email?.message}
        />

        <Button
          type="submit"
          loading={isLoading}
          className={styles.submitButton}
        >
          {t(`${namespace}.submit`)}
        </Button>
      </form>
    </AuthFormWrapper>
  );
};
