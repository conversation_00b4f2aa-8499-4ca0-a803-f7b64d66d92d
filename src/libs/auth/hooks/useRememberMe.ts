import { useEffect } from 'react';

const REMEMBER_ME_LS_KEY = 'rememberMe';

export type RememberMeUserType = {
  email: string;
  password: string;
  rememberMe: boolean;
};
interface useRememberMeProps {
  onLoadValues: (values: RememberMeUserType) => void;
}
export const useRememberMe = ({ onLoadValues }: useRememberMeProps) => {
  useEffect(() => {
    const rememberMeLS = localStorage.getItem(REMEMBER_ME_LS_KEY);
    const rememberMeUser = rememberMeLS ? JSON.parse(rememberMeLS) : null;

    if (rememberMeUser) {
      onLoadValues(rememberMeUser);
    }
  }, [onLoadValues]);

  const handleSetRememberMeValues = (values: RememberMeUserType) => {
    if (values.rememberMe) {
      localStorage.setItem(REMEMBER_ME_LS_KEY, JSON.stringify(values));
    } else {
      localStorage.removeItem(REMEMBER_ME_LS_KEY);
    }
  };

  return {
    setRememberMeValues: handleSetRememberMeValues,
  };
};
