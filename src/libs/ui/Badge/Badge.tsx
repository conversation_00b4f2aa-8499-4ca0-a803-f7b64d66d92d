import { ReactNode } from 'react';
import { mergeClasses } from '@/utils';

export interface BadgeProps {
  className?: string;
  children: ReactNode;
}

export const Badge = ({ children, className }: BadgeProps) => {
  return (
    <div
      className={mergeClasses(
        'flex items-center justify-center rounded-4xl bg-white px-4 py-2 text-sm font-medium whitespace-nowrap capitalize',
        className,
      )}
    >
      {children}
    </div>
  );
};
