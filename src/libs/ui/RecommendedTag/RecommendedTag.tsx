import { ReactNode } from 'react';
import clsx from 'clsx';
import styles from './RecommendedTag.module.css';

const SIZE_CLASSES = {
  xs: styles.xSmall,
  sm: styles.small,
  md: styles.md,
  lg: styles.large,
};

export type RecommendedTagProps = {
  top?: string;
  left?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  placement?:
    | 'top'
    | 'bottom'
    | 'left'
    | 'right'
    | 'top-left'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-right';
  children: ReactNode;
};

export const RecommendedTag = ({
  top = '0',
  left = '0',
  size = 'md',
  placement = 'left',
  children,
}: RecommendedTagProps) => {
  return (
    <span
      style={{
        top,
        left,
      }}
      className={clsx(styles.container, {
        [SIZE_CLASSES[size]]: true,
        [styles[`placement-${placement}`]]: true,
      })}
    >
      {children}
    </span>
  );
};
