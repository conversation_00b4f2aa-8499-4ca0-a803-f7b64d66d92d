import i18n from '@/apps/shop/i18n';
import LinkedIcon from '@/assets/images/linked.svg?react';
import IssueIcon from '@/assets/images/reject.svg?react';
import { VendorType } from '@/types';
import { ColorBadgeParams } from './types';

export const getClinicVendorStatusParams = (
  status: VendorType['status'],
): ColorBadgeParams => {
  switch (status) {
    case 'disconnected':
      return {
        displayName: i18n.t('client.clinicManagement.connectVendor'),
        color: 'var(--mantine-color-red-7)',
        bgColor: 'var(--mantine-color-red-1)',
        icon: <IssueIcon />,
      };
    case 'connecting':
      return {
        displayName: i18n.t('client.vendors.statuses.connected'),
        color: 'var(--mantine-color-green-7)',
        bgColor: 'var(--mantine-color-green-11)',
        icon: <LinkedIcon />,
      };
    case 'connected':
      return {
        displayName: i18n.t('client.vendors.statuses.connected'),
        color: 'var(--mantine-color-green-7)',
        bgColor: 'var(--mantine-color-green-11)',
        icon: <LinkedIcon />,
      };
    default:
      return {
        displayName: status,
        color: 'var(--mantine-color-dark-8)',
        bgColor: 'var(--mantine-color-white)',
      };
  }
};
