import { Text } from '@mantine/core';
import clsx from 'clsx';

import styles from './ColorBadge.module.css';
import { ColorBadgeParams } from './types';

interface Props<T> {
  value: T;
  paramsMapper: (value: T) => ColorBadgeParams;
  customParams?: {
    size?: string;
    rootClassName?: string;
  };
  onClick?: () => void;
}

export const ColorBadge = <T,>({
  value,
  paramsMapper,
  customParams,
  onClick,
}: Props<T>) => {
  const badgeParams = paramsMapper(value);
  const { size = 'sm', rootClassName = '' } = customParams || {};

  return (
    <div
      className={clsx(styles.status, rootClassName, {
        [styles.cursor]: Boolean(onClick),
      })}
      style={{ backgroundColor: badgeParams.bgColor }}
      onClick={onClick}
    >
      <Text size={size} c={badgeParams.color}>
        {badgeParams.icon}
        {badgeParams.displayName}
      </Text>
    </div>
  );
};
