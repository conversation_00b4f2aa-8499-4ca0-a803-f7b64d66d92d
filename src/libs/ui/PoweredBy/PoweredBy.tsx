import i18n from '@/apps/shop/i18n';
import { Logo } from '../Logo/Logo';
import clsx from 'clsx';

interface PoweredByProps {
  className?: string;
}

export const PoweredBy: React.FC<PoweredByProps> = ({ className }) => {
  return (
    <div className={clsx('flex items-center', className)}>
      <span className="mr-1 min-h-3.5">{i18n.t('common.poweredBy')}</span>
      <Logo type="full" className="h-full w-auto" />
    </div>
  );
};

PoweredBy.displayName = 'PoweredBy';
