import LogoEmblemUrl from './assets/logo-emblem.svg';
import LogoFullUrl from './assets/logo-full.svg';
import LogoNameUrl from './assets/logo-name.svg';

type LogoTypes = 'full' | 'name' | 'emblem';

interface LogoProps {
  type?: LogoTypes;
  className?: string;
}
export const Logo = ({ type = 'full', className }: LogoProps) => {
  const logos: Record<LogoTypes, string> = {
    full: LogoFullUrl,
    emblem: LogoEmblemUrl,
    name: LogoNameUrl,
  };

  return (
    <img src={logos[type]} alt="Logo" width="100%" className={className} />
  );
};
