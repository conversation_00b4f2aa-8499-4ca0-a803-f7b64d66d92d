import { useEffect, useState, forwardRef } from 'react';
import type { ReactNode } from 'react';
import styles from './Button.module.css';
import { Link } from 'react-router-dom';
import { Loader } from '@mantine/core';
import clsx from 'clsx';

export type ButtonBaseProps = {
  children: ReactNode;
  loading?: boolean;
  variant?: 'default' | 'secondary' | 'white' | 'unstyled';
  p?: string;
  size?: 'sm' | 'md';
};

type ButtonAsButton = ButtonBaseProps &
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    href?: never;
    to?: never;
    disabled?: boolean;
  };

type ButtonAsLink = ButtonBaseProps &
  React.AnchorHTMLAttributes<HTMLAnchorElement> & {
    href?: string;
    to?: string;
    disabled?: boolean;
  };

export type ButtonProps = ButtonAsButton | ButtonAsLink;

export const Button = forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  ButtonProps
>(({ className: extraClassName, ...props }, ref) => {
  const {
    children,
    loading = false,
    disabled = false,
    variant = 'default',
    p = '0 1rem',
    size = 'md',
  } = props;

  const [showLoader, setShowLoader] = useState(loading);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (loading) {
      setShowLoader(true);
    } else {
      timeout = setTimeout(() => setShowLoader(false), 300);
    }
    return () => clearTimeout(timeout);
  }, [loading]);

  const commonClassName = clsx(
    variant === 'unstyled' ? styles.unstyled : styles.container,
    extraClassName,
    {
      [styles.secondary]: variant === 'secondary',
      [styles.white]: variant === 'white',
      [styles.small]: size === 'sm',
      [styles.loading]: showLoader,
    },
  );

  const styleInline = variant === 'unstyled' ? {} : { padding: p };

  const content = (
    <>
      <div className={styles.loaderWrapper}>
        <Loader color="#FFF" size="sm" />
      </div>
      <div className={styles.contentWrapper} style={styleInline}>
        {children}
      </div>
    </>
  );

  if ('to' in props && props.to) {
    const { to, onClick, ...rest } = props;
    return (
      <Link
        ref={ref as React.Ref<HTMLAnchorElement>}
        to={to}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </Link>
    );
  }

  if ('href' in props && props.href) {
    const { href, onClick, ...rest } = props;
    return (
      <a
        ref={ref as React.Ref<HTMLAnchorElement>}
        href={href}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </a>
    );
  }

  const { onClick, loading: loadingProps, ...rest } = props;

  return (
    <button
      ref={ref as React.Ref<HTMLButtonElement>}
      className={commonClassName}
      onClick={onClick as React.MouseEventHandler<HTMLButtonElement>}
      disabled={loadingProps || disabled}
      {...(rest as React.ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {content}
    </button>
  );
});

Button.displayName = 'Button';
