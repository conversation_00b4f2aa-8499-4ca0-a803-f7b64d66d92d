.container {
  color: #222;
  background-color: var(--mantine-color-yellow-5);
  position: relative;
  display: inline-block;
  overflow: hidden;
  border-radius: 0.4rem;
  height: 40px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
  width: 100%;
  text-decoration: none;
  white-space: nowrap;
  padding: 0;

  &:disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  &:focus-visible {
    outline: 2px solid var(--mantine-color-yellow-6);
    outline-offset: 0.125rem;
  }
}

.secondary {
  color: #fff;
  background-color: var(--mantine-color-light-blue-9);
}

.white {
  background-color: #fff;
  border: 1px solid rgba(34, 34, 34, 0.15);
}

.unstyled {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  line-height: 1;
  width: auto;
  min-width: auto;
  height: auto;
  border-radius: 0;
  overflow: visible;
  white-space: normal;
}

.unstyled:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.unstyled:focus {
  outline: none;
}

.unstyled:focus-visible {
  outline: 2px solid var(--mantine-color-blue-6);
  outline-offset: 2px;
}

.small {
  height: 32px;
}

.contentWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  transition: transform 0.3s ease;
  will-change: transform;
  transform: translateY(0%);
  opacity: 1;
}

.loading .contentWrapper {
  transform: translateY(100%);
  opacity: 0;
}

.loaderWrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  will-change: transform;
  transform: translateY(-100%);
  opacity: 0;
  pointer-events: none;
}

.loading .loaderWrapper {
  transform: translateY(0%);
  opacity: 1;
}
