import { ReactNode } from 'react';
import clsx from 'clsx';

import { Flex } from '@/libs/ui/Flex/Flex';
import styles from './Alert.module.css';

interface AlertProps {
  type?: 'info' | 'error';
  icon?: ReactNode;
  children: ReactNode;
}
export const Alert = ({ icon, children, type }: AlertProps) => (
  <Flex
    align="center"
    px=".75rem"
    py="0.5rem"
    w="100%"
    className={clsx(styles.container, {
      [styles.error]: type === 'error',
    })}
  >
    {icon ? <div className="mr-2.5 w-7">{icon}</div> : null}
    <div>{children}</div>
  </Flex>
);
