import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { SidebarNavLink, type SidebarNavLinkProps } from './SidebarNavLink';

export interface SidebarNavProps {
  title?: string;
  links: SidebarNavLinkProps[];
  theme?: 'dark' | 'light';
}
export const SidebarNav = ({
  title,
  links,
  theme = 'light',
}: SidebarNavProps) => {
  return (
    <Flex direction="column">
      {title ? (
        <Text
          c={theme === 'light' ? '#73757C' : 'rgba(255, 255, 255, 0.7)'}
          p="0.75rem"
          mt="0.25rem"
        >
          {title}
        </Text>
      ) : (
        ''
      )}
      {links.map((link) => (
        <SidebarNavLink key={link.label} {...link} theme={theme} />
      ))}
    </Flex>
  );
};
