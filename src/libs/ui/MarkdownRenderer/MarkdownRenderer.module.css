.container {
  color: #344054;
  font-size: 0.875rem;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: #333;
    font-size: 1rem;
  }

  p {
    color: #344054;
    font-size: 0.875rem;
  }

  a,
  a:visited,
  a:hover {
    color: #344054;
    text-underline-offset: 0.25rem;
  }

  cite {
    font-weight: bold;
  }

  table {
    width: 100%;
  }

  table > tbody > tr > td:first-child {
    font-weight: bold;
  }

  table th,
  table td {
    color: #222;
    text-align: left;
    padding: 1rem 1rem;
    border-bottom: 1px solid #eaecf0;
  }

  table thead {
    background-color: #f2f4f7;
  }

  table th {
    font-weight: bold;
  }

  ol,
  ul {
    color: #333;
  }

  li {
    padding-left: 0.25rem;
    padding-top: 0.3rem;
  }

  ol > li::marker {
    font-weight: bold;
  }

  ul > li::marker {
    color: rgba(123, 123, 123, 0.7);
  }

  mark {
    padding-left: 0.2rem;
    padding-right: 0.2rem;
    background-color: #fadf82;
    color: #222222;
  }
}
