import { convertMarkdownToHtml } from './utils/convertMarkdownToHtml';
import styles from './MarkdownRenderer.module.css';

interface MarkdownRendererProps {
  markdown: string;
  className?: string;
}
export const MarkdownRenderer = ({
  markdown,
  className = '',
}: MarkdownRendererProps) => {
  return (
    <div
      className={`${styles.container} ${className}`}
      dangerouslySetInnerHTML={{
        __html: convertMarkdownToHtml(markdown),
      }}
    />
  );
};
