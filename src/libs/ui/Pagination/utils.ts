type Value = string | number;

export const getStartIndex = (page: Value, itemsPerPage: Value): number =>
  (Number(page) - 1) * Number(itemsPerPage) + 1;

export const getEndIndex = (
  startIndex: Value,
  itemsPerPage: Value,
  total: Value,
): number =>
  Math.min(Number(startIndex) + Number(itemsPerPage) - 1, Number(total));

export const getTotalPage = (total: Value, itemsPerPage: Value): number =>
  Math.ceil(Number(total) / Number(itemsPerPage));
