body {
  /* TODO: Why? */
  .control {
    border: none;
  }
}

.container {
  max-width: var(--container-max-width);
  width: 100%;
  border-radius: 0.625rem;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 1.5rem;
  margin: 2rem auto 0;
}

.limits {
  display: inline-flex;
  align-items: center;
  column-gap: 1rem;
}

.limitButtons {
  padding: 0.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  border: none;
  line-height: 1;
  background-color: transparent;
}

.limitButtonsActive {
  color: #fff;
  background-color: var(--mantine-color-dark-8);
}

@media (min-width: 1100px) {
  .container {
    align-items: center;
    flex-direction: row;
  }
}

@media (min-width: 900px) {
  .actionBox {
    flex-direction: row;
    align-items: center;
  }
}
