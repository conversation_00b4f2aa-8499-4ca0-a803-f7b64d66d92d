import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Combobox } from '../Combobox';
import { data } from './mock-data';

const meta: Meta<typeof Combobox> = {
  title: 'UI/Combobox',
  component: Combobox,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  render: () => {
    const simpleData = data.map((item) => item.name);
    const [selected, setSelected] = useState('');

    const handleChange = (value: unknown) => {
      setSelected(value as string);
    };

    return (
      <div style={{ width: '16rem' }}>
        <Combobox value={selected} onChange={handleChange}>
          <div style={{ position: 'relative' }}>
            <Combobox.Input
              label="Choose an option"
              placeholder="Select an option..."
              onChange={(event) => setSelected(event.target.value)}
              displayValue={(value) => (value as string) || selected}
            />
            <Combobox.Options>
              {simpleData.map((option) => (
                <Combobox.Option key={option} value={option}>
                  {option}
                </Combobox.Option>
              ))}
            </Combobox.Options>
          </div>
        </Combobox>
      </div>
    );
  },
};

export const CustomDisplay: Story = {
  render: () => {
    const [selectedProduct, setSelectedProduct] = useState<{
      id: string;
      name: string;
    } | null>(null);

    const handleChange = (value: unknown) => {
      setSelectedProduct(value as { id: string; name: string } | null);
    };

    return (
      <Combobox value={selectedProduct} onChange={handleChange}>
        <Combobox.Input
          displayValue={(product) =>
            product
              ? `${(product as { name: string }).name} (${(product as { name: string }).name})`
              : ''
          }
          placeholder="Select a product..."
        />
        <Combobox.Options>
          {data.map((product) => (
            <div
              key={product.id}
              style={{
                cursor: 'pointer',
                paddingLeft: '0.75rem',
                paddingRight: '0.75rem',
                paddingTop: '0.5rem',
                paddingBottom: '0.5rem',
              }}
            >
              <Combobox.Option value={product}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                  }}
                >
                  <div
                    style={{
                      height: '1rem',
                      width: '1rem',
                      borderRadius: '50%',
                      border: '1px solid #D1D5DB',
                    }}
                  />
                  <span>{product.name}</span>
                </div>
              </Combobox.Option>
            </div>
          ))}
        </Combobox.Options>
      </Combobox>
    );
  },
};

export const LoadingWithIcon: Story = {
  render: () => {
    const simpleData = data.map((item) => item.name);
    const [selected, setSelected] = useState('');

    const handleChange = (value: unknown) => {
      setSelected(value as string);
    };

    return (
      <div style={{ width: '16rem' }}>
        <Combobox
          value={selected}
          onChange={handleChange}
          isLoading={!selected}
        >
          <div style={{ position: 'relative' }}>
            <Combobox.Input
              label="Loading State"
              placeholder="Type to search..."
              displayValue={(value) => (value as string) || selected}
            />
            <Combobox.Options>
              {simpleData.map((option) => (
                <Combobox.Option key={option} value={option}>
                  {option}
                </Combobox.Option>
              ))}
            </Combobox.Options>
          </div>
        </Combobox>
        <p
          style={{
            marginTop: '0.5rem',
            fontSize: '0.875rem',
            color: '#6B7280',
          }}
        >
          Loading state shows a spinner icon
        </p>
      </div>
    );
  },
};

export const AsyncSearch: Story = {
  render: () => {
    const [selected, setSelected] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [results, setResults] = useState<string[]>([]);
    const simpleData = data.map((item) => item.name);

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;

      if (value.length > 0) {
        setIsLoading(true);

        setTimeout(() => {
          const filtered = simpleData.filter((option) =>
            option.toLowerCase().includes(value.toLowerCase()),
          );
          setResults(filtered);
          setIsLoading(false);
        }, 800);
      } else {
        setResults([]);
        setIsLoading(false);
      }
    };

    const handleChange = (value: unknown) => {
      setSelected(value as string);
      setResults([]);
    };

    return (
      <div style={{ width: '20rem' }}>
        <Combobox
          value={selected}
          onChange={handleChange}
          isLoading={isLoading}
        >
          <div style={{ position: 'relative' }}>
            <Combobox.Input
              label="Search Phones"
              placeholder="Type to search phones..."
              onChange={handleInputChange}
              displayValue={() => selected}
            />
            <Combobox.Options>
              {results.map((phone) => (
                <Combobox.Option key={phone} value={phone}>
                  {phone}
                </Combobox.Option>
              ))}
            </Combobox.Options>
          </div>
        </Combobox>
      </div>
    );
  },
};

export const EnterKeySearch: Story = {
  render: () => {
    const [query, setQuery] = useState('');
    const simpleData = data.map((item) => item.name);

    const filteredSuggestions = simpleData.filter((item) =>
      item.toLowerCase().includes(query.toLowerCase()),
    );

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      setQuery(event.target.value);
    };

    const handleEnterPress = (searchTerm: string) => {
      if (searchTerm.trim()) {
        setQuery(searchTerm);
      }
    };

    const handleSuggestionSelect = (value: unknown) => {
      const suggestion = value as string;
      setQuery(suggestion);
    };

    return (
      <div style={{ width: '20rem' }}>
        {query && (
          <div className="mt-3 rounded bg-green-50 p-3">
            <p className="text-sm font-medium text-green-800">{query}</p>
          </div>
        )}
        <Combobox value={query} onChange={handleSuggestionSelect}>
          <div style={{ position: 'relative' }}>
            <Combobox.Input
              label="Search products"
              placeholder="Type and press Enter to search..."
              onChange={handleInputChange}
              onEnterPress={handleEnterPress}
              displayValue={() => query}
            />
            <Combobox.Options>
              {filteredSuggestions.map((suggestion) => (
                <Combobox.Option key={suggestion} value={suggestion}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span style={{ marginRight: '0.5rem' }}>🔍</span>
                    {suggestion}
                  </div>
                </Combobox.Option>
              ))}
            </Combobox.Options>
          </div>
        </Combobox>
      </div>
    );
  },
};
