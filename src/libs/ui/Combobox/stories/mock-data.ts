export const data = [
  { id: 'simcro', name: 'simcro' },
  { id: 'simbadol', name: 'simbadol' },
  { id: 'simplicef', name: 'simplicef' },
  { id: 'simparica', name: 'simparica' },
  { id: 'simplera', name: 'simplera' },
  { id: 'sim-medium', name: 'sim medium' },
  { id: 'simplehuman', name: 'simplehuman' },
  { id: 'simplescope', name: 'simplescope' },
  { id: 'simvastatin', name: 'simvastatin' },
  { id: 'simple-green', name: 'simple green' },
  { id: 'simparica-ch', name: 'simparica ch' },
  { id: 'simparica-chw', name: 'simparica chw' },
  { id: 'simplex-iv-set', name: 'simplex iv set' },
  { id: 'simplex-iv-bell', name: 'simplex iv bell' },
  { id: 'simbadol-c-iii', name: 'simbadol c iii' },
  { id: 'simcro-draxxin', name: 'simcro draxxin /' },
  { id: 'simpli-dry-piglet', name: 'simpli dry piglet' },
  { id: 'simplicef-tablets', name: 'simplicef tablets' },
  { id: 'simethicone-drops', name: 'simethicone drops' },
  { id: 'simplex-with-luer', name: 'simplex with luer' },
  { id: 'simplicef-tablets-2', name: 'simplicef tablets' },
  { id: 'simcro-stf-syringe', name: 'simcro stf syringe' },
  { id: 'simplify-non-woven', name: 'simplify non woven' },
  { id: 'simplehuman-code-r', name: 'simplehuman code r' },
  { id: 'simplify-under-the', name: 'simplify under the' },
  { id: 'simplehuman-code-v', name: 'simplehuman code v' },
  { id: 'simbadol-injection', name: 'simbadol injection' },
  { id: 'simparica-chewables', name: 'simparica chewables' },
  { id: 'simvastatin-tablets', name: 'simvastatin tablets' },
  { id: 'simple-solution-cat', name: 'simple solution cat' },
  { id: 'simple-rewards-baked', name: 'simple rewards baked' },
  { id: 'sims-uterine-curette', name: 'sims uterine curette' },
  { id: 'sim-medium-criterion', name: 'sim medium criterion' },
  { id: 'simmons-citrate-agar', name: 'simmons citrate agar' },
  { id: 'sims-uterine-scissors', name: 'sims uterine scissors' },
  { id: 'simple-rewards-veggie', name: 'simple rewards veggie' },
  { id: 'simple-rewards-treats', name: 'simple rewards treats' },
  { id: 'simple-green-smp13006', name: 'simple green smp13006' },
  { id: 'simple-rewards-banana', name: 'simple rewards banana' },
  { id: 'simple-houseware-mesh', name: 'simple houseware mesh' },
  { id: 'simplex-vented-funnel', name: 'simplex vented funnel' },
  { id: 'simcro-sekurus-syringe', name: 'simcro sekurus syringe' },
  { id: 'simethicone-infant-gas', name: 'simethicone infant gas' },
  { id: 'simple-houseware-heavy', name: 'simple houseware heavy' },
  { id: 'simethicone-drops-oral', name: 'simethicone drops oral' },
  { id: 'simplera-otic-solution', name: 'simplera otic solution' },
  { id: 'simple-modern-insulated', name: 'simple modern insulated' },
  { id: 'simetufy-magnetic-glass', name: 'simetufy magnetic glass' },
  { id: 'simvastatin-tablets-usp', name: 'simvastatin tablets usp' },
  { id: 'simplify-bed-risers-bed', name: 'simplify bed risers/bed' },
  { id: 'simplera-otic-solution-2', name: 'simplera otic solution' },
  { id: 'simparica-trio-chewable', name: 'simparica trio chewable' },
  { id: 'simple-syrup-unflavored', name: 'simple syrup unflavored' },
  { id: 'simplescope-vision-center', name: 'simplescope vision center' },
  { id: 'simplicity-basic-underpad', name: 'simplicity basic underpad' },
  { id: 'simple-rewards-strawberry', name: 'simple rewards strawberry' },
  { id: 'simple-green-concentrated', name: 'simple green concentrated' },
  { id: 'simparica-chewable-tablets', name: 'simparica chewable tablets' },
  { id: 'simple-tear-premium-cohesive', name: 'simple-tear premium cohesive' },
  { id: 'simethicone-chewable-tablets', name: 'simethicone chewable tablets' },
  { id: 'simpliscale-scaler-posterior', name: 'simpliscale scaler posterior' },
  { id: 'simpliscale-universal-gracey', name: 'simpliscale universal gracey' },
  { id: 'simbadol-buprenorphine', name: 'simbadol buprenorphine' },
];
