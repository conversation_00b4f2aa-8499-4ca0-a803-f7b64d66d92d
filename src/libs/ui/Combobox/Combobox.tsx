import {
  useCallback,
  useEffect,
  useRef,
  useState,
  type ReactNode,
} from 'react';
import { ComboboxContext, type ComboboxContextType } from './ComboboxContext';
import styles from './Combobox.module.css';
import { ComboboxInput } from './components/ComboboxInput/ComboboxInput';
import { ComboboxOption } from './components/ComboboxOption/ComboboxOption';
import { ComboboxOptions } from './components/ComboboxOptions/ComboboxOptions';

interface ComboboxProps {
  value?: unknown;
  onChange?: (value: unknown) => void;
  children: ReactNode;
  disabled?: boolean;
  getOptionValue?: (option: unknown) => string | number;
  isLoading?: boolean;
}

export function Combobox({
  value,
  onChange,
  children,
  disabled = false,
  getOptionValue,
  isLoading = false,
}: ComboboxProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const optionRefs = useRef<Map<unknown, HTMLElement | null>>(new Map());
  const [optionOrder, setOptionOrder] = useState<unknown[]>([]);

  const openCombobox = useCallback(() => {
    if (!disabled) {
      setIsOpen(true);
    }
  }, [disabled]);

  const closeCombobox = useCallback(() => {
    setIsOpen(false);
    setHighlightedIndex(-1);
  }, []);

  const selectOption = useCallback(
    (optionValue: unknown) => {
      onChange?.(optionValue);
      setInputValue('');
      closeCombobox();
    },
    [onChange, closeCombobox],
  );

  const navigateOptions = useCallback(
    (direction: 'up' | 'down') => {
      if (optionOrder.length === 0) return;

      setHighlightedIndex((prevIndex) => {
        if (direction === 'down') {
          return prevIndex < optionOrder.length - 1 ? prevIndex + 1 : 0;
        } else {
          return prevIndex > 0 ? prevIndex - 1 : optionOrder.length - 1;
        }
      });
    },
    [optionOrder],
  );

  const selectHighlighted = useCallback(() => {
    if (highlightedIndex >= 0 && highlightedIndex < optionOrder.length) {
      const highlightedOption = optionOrder[highlightedIndex];
      selectOption(highlightedOption);
    }
  }, [highlightedIndex, optionOrder, selectOption]);

  const resetHighlight = useCallback(() => {
    setHighlightedIndex(-1);
  }, []);

  const recreateOptionsFromChildren = useCallback(
    (childrenOptions: unknown[]) => {
      setOptionOrder([]);
      optionRefs.current.clear();
      setHighlightedIndex(-1);
      setOptionOrder(childrenOptions);
    },
    [],
  );

  const registerOption = useCallback(
    (optionValue: unknown, element: HTMLElement | null) => {
      optionRefs.current.set(optionValue, element);
    },
    [],
  );

  const unregisterOption = useCallback((optionValue: unknown) => {
    optionRefs.current.delete(optionValue);
  }, []);

  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: Event) => {
      const target = event.target as Element;
      if (!target.closest('[data-combobox]')) {
        closeCombobox();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, closeCombobox]);

  useEffect(() => {
    const refs = optionRefs.current;
    return () => {
      refs.clear();
    };
  }, []);

  useEffect(() => {
    if (isOpen) {
      setHighlightedIndex(-1);
    } else {
      setOptionOrder([]);
      optionRefs.current.clear();
    }
  }, [isOpen]);

  useEffect(() => {
    setHighlightedIndex(-1);
  }, [optionOrder]);

  const contextValue: ComboboxContextType = {
    isOpen,
    selectedValue: value,
    options: optionOrder,
    inputValue,
    highlightedIndex,
    openCombobox,
    closeCombobox,
    selectOption,
    setInputValue,
    registerOption,
    unregisterOption,
    getOptionValue,
    isLoading,
    navigateOptions,
    selectHighlighted,
    resetHighlight,
    recreateOptionsFromChildren,
  };

  return (
    <ComboboxContext.Provider value={contextValue}>
      <div data-combobox className={styles.comboboxContainer}>
        {children}
      </div>
    </ComboboxContext.Provider>
  );
}

Combobox.Input = ComboboxInput;
Combobox.Options = ComboboxOptions;
Combobox.Option = ComboboxOption;
