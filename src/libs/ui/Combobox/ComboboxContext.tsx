import { createContext, useContext } from 'react';

export interface ComboboxContextType {
  isOpen: boolean;
  selectedValue?: unknown;
  options: unknown[];
  inputValue: string;
  highlightedIndex: number;
  openCombobox: () => void;
  closeCombobox: () => void;
  selectOption: (value: unknown) => void;
  setInputValue: (value: string) => void;
  registerOption: (value: unknown, element: HTMLElement | null) => void;
  unregisterOption: (value: unknown) => void;
  getOptionValue?: (option: unknown) => string | number;
  isLoading?: boolean;
  navigateOptions: (direction: 'up' | 'down') => void;
  selectHighlighted: () => void;
  resetHighlight: () => void;
  recreateOptionsFromChildren: (childrenOptions: unknown[]) => void;
}

export const ComboboxContext = createContext<ComboboxContextType | null>(null);

export function useComboboxContext() {
  const context = useContext(ComboboxContext);
  if (!context) {
    throw new Error('Combobox components must be used within a Combobox');
  }
  return context;
}
