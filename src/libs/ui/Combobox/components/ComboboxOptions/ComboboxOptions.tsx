import { type ReactNode, useEffect, Children, isValidElement } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import clsx from 'clsx';
import styles from './ComboboxOptions.module.css';
import { useComboboxContext } from '../../ComboboxContext';

const comboboxOptionsVariants = cva(styles.optionsBase, {
  variants: {
    size: {
      sm: styles.optionsSm,
      md: styles.optionsMd,
      lg: styles.optionsLg,
    },
    variant: {
      default: styles.optionsDefault,
    },
  },
  defaultVariants: {
    size: 'md',
    variant: 'default',
  },
});

interface ComboboxOptionsProps
  extends VariantProps<typeof comboboxOptionsVariants> {
  children: ReactNode;
  className?: string;
}

export const ComboboxOptions = ({
  children,
  className,
  size,
  variant,
}: ComboboxOptionsProps) => {
  const { isOpen, recreateOptionsFromChildren } = useComboboxContext();

  useEffect(() => {
    if (isOpen && children) {
      const childrenArray = Children.toArray(children);
      const optionValues = childrenArray
        .filter(
          (child) => isValidElement(child) && child.props?.value !== undefined,
        )
        .map((child) => (isValidElement(child) ? child.props.value : undefined))
        .filter((value) => value !== undefined);

      recreateOptionsFromChildren(optionValues);
    }
  }, [children, isOpen, recreateOptionsFromChildren]);

  if (!isOpen) return null;

  const hasNoChildren =
    !children || (Array.isArray(children) && children.length === 0);

  if (hasNoChildren) return null;

  return (
    <div
      className={clsx(comboboxOptionsVariants({ size, variant }), className)}
    >
      {children}
    </div>
  );
};
