.optionBase {
  cursor: pointer;
  user-select: none;
  padding: 0.5rem 0.75rem;
  transition: background-color 0.15s ease-in-out;
  word-wrap: break-word;
  overflow-wrap: break-word;
  border-radius: 0.25rem;
}

.optionBase:hover {
  background-color: #f1f3f5;
}

.optionBase[data-highlighted='true'] {
  background-color: #f1f3f5;
}

.optionBase[data-highlighted='true']:hover {
  background-color: #f1f3f5;
}
