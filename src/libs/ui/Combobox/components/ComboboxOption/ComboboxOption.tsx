import { useRef, useEffect, type MouseEvent, type ReactNode } from 'react';
import clsx from 'clsx';
import styles from './ComboboxOption.module.css';
import { useComboboxContext } from '../../ComboboxContext';

interface ComboboxOptionProps {
  value: unknown;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function ComboboxOption({
  value,
  children,
  className,
  disabled = false,
}: ComboboxOptionProps) {
  const {
    selectOption,
    registerOption,
    unregisterOption,
    getOptionValue,
    options,
    highlightedIndex,
  } = useComboboxContext();

  const optionRef = useRef<HTMLDivElement>(null);

  const isHighlighted = (() => {
    if (highlightedIndex < 0 || highlightedIndex >= options.length) {
      return false;
    }

    const currentIndex = options.findIndex((option) => {
      if (!getOptionValue) {
        return option === value;
      }
      try {
        return getOptionValue(option) === getOptionValue(value);
      } catch {
        return option === value;
      }
    });

    return currentIndex === highlightedIndex && currentIndex >= 0;
  })();

  useEffect(() => {
    registerOption(value, optionRef.current);
    return () => unregisterOption(value);
  }, [value, registerOption, unregisterOption]);

  useEffect(() => {
    if (isHighlighted && optionRef.current) {
      optionRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [isHighlighted]);

  const handleClick = (event: MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!disabled) {
      selectOption(value);
    }
  };

  return (
    <div
      ref={optionRef}
      className={clsx(styles.optionBase, className)}
      onClick={handleClick}
      data-highlighted={isHighlighted}
      data-disabled={disabled}
    >
      {children}
    </div>
  );
}
