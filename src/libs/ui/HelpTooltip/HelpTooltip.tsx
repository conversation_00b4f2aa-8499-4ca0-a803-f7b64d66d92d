import { Tooltip } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import QuestionIcon from './assets/question.svg?react';

interface HelpTooltipProps {
  message: string;
}

export const HelpTooltip = ({ message }: HelpTooltipProps) => (
  // TODO: Support keyboard navigation
  <Tooltip
    label={message}
    position="right-start"
    offset={{ mainAxis: -10, crossAxis: 20 }}
    maw="18rem"
    multiline
  >
    <Button variant="unstyled" type="button">
      <QuestionIcon />
    </Button>
  </Tooltip>
);
