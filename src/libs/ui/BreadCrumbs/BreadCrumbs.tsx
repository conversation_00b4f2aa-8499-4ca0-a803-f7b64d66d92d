import { Flex } from '@/libs/ui/Flex/Flex';
import { Link } from 'react-router-dom';
import styles from './BreadCrumbs.module.css';
import { Fragment } from 'react/jsx-runtime';

interface BreadCrumbsProps {
  items: {
    path: string;
    search: string;
    name?: string;
  }[];
}

const capitalizeFirstLetter = (text: string) =>
  String(text).charAt(0).toUpperCase() + String(text).slice(1);

const getNameByPath = (path: string) => {
  if (path === '/') {
    return 'Home';
  }

  const paths = path.split('/');
  const lastPath = paths[1];

  return lastPath.replace('-', ' ');
};

export const BreadCrumbs = ({ items }: BreadCrumbsProps) => {
  return (
    <Flex align="center" className={styles.wrap}>
      {items.map(({ path, name, search }, index) => (
        <Fragment key={+index}>
          {index !== 0 ? (
            <Flex miw="2rem" align="center" justify="center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="17"
                viewBox="0 0 16 17"
                fill="none"
              >
                <path
                  d="M6.47148 11.7998L10.2761 7.9951L6.47148 4.19043L5.52881 5.1331L8.39081 7.9951L5.52881 10.8571L6.47148 11.7998Z"
                  fill="black"
                />
              </svg>
            </Flex>
          ) : null}
          <Link to={path + (search ?? '')} className={styles.link}>
            {capitalizeFirstLetter(name ? name : getNameByPath(path))}
          </Link>
        </Fragment>
      ))}
    </Flex>
  );
};
