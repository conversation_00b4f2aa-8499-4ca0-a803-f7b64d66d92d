.section {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  box-shadow: 0 0 5px 2px #0000000d;
  border-radius: 0.5rem;
  width: 100%;
  height: max-content;
  border: 1px solid var(--mantine-color-dark-3);
  background-color: var(--mantine-color-dark-1);
  position: sticky;
  z-index: 100;
  top: 1rem;

  .clearCart {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .clearCartText {
    color: var(--mantine-color-red-6);
    font-size: 0.875rem;
    text-decoration: underline;
  }

  .vendorSummary {
    display: flex;
    flex-direction: column;

    .subtotal {
      font-size: 0.875rem;
    }
  }

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.25rem;
  }
}
