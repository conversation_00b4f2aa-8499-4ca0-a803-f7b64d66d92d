import React from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Icon } from '@/libs/icons/Icon';

const mockItems = [
  {
    id: 1,
    name: 'Product 1',
    image: 'https://m.media-amazon.com/images/I/617KOZJSYmS._SY500_.jpg',
    quantity: 1,
  },
  {
    id: 2,
    name: 'Product 2',
    image: 'https://m.media-amazon.com/images/I/81ojGqyr+lL._SY500_.jpg',
    quantity: 2,
  },
  {
    id: 3,
    name: 'Product 3',
    image: 'https://m.media-amazon.com/images/I/617KOZJSYmS._SY500_.jpg',
    quantity: 8,
  },
];

export const SavedItemsPanel = () => {
  const items = mockItems;
  return (
    <CollapsiblePanel
      header={
        <div className="flex w-full items-center justify-between bg-[#FAFAFA] pr-16 pl-4">
          <h4 className="text-sm font-medium">Saved Items (4)</h4>
          <Button variant="unstyled">
            <span className="text-xs font-medium text-[#447bfd]">
              Move all to cart
            </span>
          </Button>
        </div>
      }
      content={
        <div className="bg-[#FAFAFA] p-4">
          <div className="border-1 border-black/[0.04] bg-[#fff] p-4">
            {items.map(({ id, name, image, quantity }) => (
              <div
                key={id}
                className="mb-2 flex items-center justify-between border-b border-black/[0.04] pb-2 [&:last-child]:m-0 [&:last-child]:border-none [&:last-child]:p-0"
              >
                <div className="flex items-center gap-2">
                  <div className="flex h-5 w-7 items-center justify-center rounded-sm bg-[#E5FCFD] text-[10px] font-medium">
                    {quantity}
                  </div>
                  <div className="relative h-12 w-12 rounded-md border-1 border-black/[0.04]">
                    <img
                      src={image}
                      alt={name}
                      className="absolute inset-0 h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-sm font-medium">{name}</h5>
                    <p className="text-xs">
                      <span className="mr-1 text-[#666666] line-through">
                        $49.22
                      </span>
                      <span className="font-medium text-[#333]">$49.22</span>
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Button variant="unstyled">
                    <Icon name="trash" color="#667085" size="1rem" />
                  </Button>
                  <div className="h-4 w-px bg-[#D0D5DD]" />
                  <Button className="flex items-center gap-1">
                    <Icon name="cart" color="#667085" size="1.25rem" />
                    <Icon name="plus" color="#667085" size="0.875rem" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      }
    />
  );
};
