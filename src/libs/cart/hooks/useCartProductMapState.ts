import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useMemo } from 'react';

export const useCartProductMapState = () => {
  const { vendors } = useCartStore();

  const mapState = useMemo<Record<string, { quantity: number }>>(
    () =>
      vendors
        .flatMap(({ items }) => items)
        .reduce(
          (acc, { productOfferId, quantity }) => ({
            ...acc,
            [productOfferId]: { quantity },
          }),
          {},
        ),
    [vendors],
  );

  return mapState;
};
