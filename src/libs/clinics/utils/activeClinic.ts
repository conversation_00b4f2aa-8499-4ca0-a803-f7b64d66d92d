import { ActiveClinicProps } from '../types';

const ACTIVE_CLINIC_LS_KEY = 'activeClinic';

export const setActiveClinic = (clinic: ActiveClinicProps | null) => {
  if (clinic) {
    localStorage.setItem(ACTIVE_CLINIC_LS_KEY, JSON.stringify(clinic));
  }
};

export const clearActiveClinic = () => {
  localStorage.removeItem(ACTIVE_CLINIC_LS_KEY);
};

export const getActiveClinic = () => {
  const activeClinicString = localStorage.getItem(ACTIVE_CLINIC_LS_KEY) ?? '';

  if (!activeClinicString) {
    return null;
  }

  try {
    return JSON.parse(activeClinicString) as ActiveClinicProps;
  } catch {
    clearActiveClinic();
    return null;
  }
};
