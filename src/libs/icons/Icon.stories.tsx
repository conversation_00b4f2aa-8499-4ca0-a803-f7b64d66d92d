import type { Meta, StoryObj } from '@storybook/react';
import { Icon } from './Icon';

type Story = StoryObj<typeof Icon>;

const AVAILABLE_ICONS = ['magnifier', 'moreOptions', 'download'] as const;
type IconName = (typeof AVAILABLE_ICONS)[number];

const meta: Meta<typeof Icon> = {
  title: 'UI/Icons',
  component: Icon,
  parameters: {
    docs: {
      description: {
        component:
          'Reusable SVG icon library with support for different sizes and colors.',
      },
    },
  },
  argTypes: {
    name: {
      control: 'select',
      options: AVAILABLE_ICONS,
      description: 'Icon name',
    },
    size: {
      control: 'text',
      description: 'Icon size (px, rem, em)',
    },
    color: {
      control: 'color',
      description: 'Icon color',
    },
  },
};

export default meta;

export const Playground: Story = {
  args: {
    name: 'magnifier',
    size: '24px',
    color: '#007bff',
  },
};

export const IconLibrary: Story = {
  render: () => (
    <div style={{ padding: '24px' }}>
      <h2 style={{ marginBottom: '24px', fontSize: '24px', fontWeight: '600' }}>
        Icon Library
      </h2>
      <div
        style={{
          display: 'flex',
          gap: '12px',
        }}
      >
        {AVAILABLE_ICONS.map((iconName: IconName) => (
          <div
            key={iconName}
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '0.5rem',
              border: '1px solid #e0e0e0',
              width: '120px',
              height: '100px',
              borderRadius: '0.5rem',
            }}
          >
            <Icon name={iconName} size="32px" color="#333" />
            <span
              style={{
                marginTop: '12px',
                fontSize: '14px',
                fontWeight: '500',
                color: '#333',
                textAlign: 'center',
              }}
            >
              {iconName}
            </span>
          </div>
        ))}
      </div>
    </div>
  ),
};
