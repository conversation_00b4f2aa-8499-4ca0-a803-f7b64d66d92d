import React, { createContext, useContext, useRef, useEffect } from 'react';
import { useLocation, Location, Outlet } from 'react-router-dom';

const MAX_HISTORY_LENGTH = 10;
const HISTORY_LOCAL_STORAGE_KEY = 'HISTORY_LOCAL_STORAGE_KEY';

interface NavigationHistoryContextType {
  history: Location[];
}

const NavigationHistoryContext = createContext<
  NavigationHistoryContextType | undefined
>(undefined);

const getHistoryInitialState = () => {
  const rawHistoryOnLocalStorage = window.localStorage.getItem(
    HISTORY_LOCAL_STORAGE_KEY,
  );

  try {
    const parsed = JSON.parse(rawHistoryOnLocalStorage ?? '');
    if (!Array.isArray(parsed)) {
      throw new Error('Invalid history stored');
    }
    return parsed as Location[];
  } catch {
    window.localStorage.setItem(HISTORY_LOCAL_STORAGE_KEY, '[]');
    return [];
  }
};

export const NavigationHistoryProvider = () => {
  const historyRef = useRef<Location[]>(getHistoryInitialState());
  const location = useLocation();

  useEffect(() => {
    return () => {
      const newHistory = historyRef.current;
      if (newHistory.length === MAX_HISTORY_LENGTH) {
        newHistory.shift();
      }

      newHistory.push({ ...location });

      window.localStorage.setItem(
        HISTORY_LOCAL_STORAGE_KEY,
        JSON.stringify(newHistory),
      );
      historyRef.current = newHistory;
    };
  }, [location]);

  return (
    <NavigationHistoryContext.Provider value={{ history: historyRef.current }}>
      <Outlet />
    </NavigationHistoryContext.Provider>
  );
};

export const useNavigationHistory = (): Location[] => {
  const context = useContext(NavigationHistoryContext);

  if (!context) {
    throw new Error(
      'useNavigationHistory must be used within a NavigationHistoryProvider',
    );
  }

  return context.history;
};

export { NavigationHistoryContext };
