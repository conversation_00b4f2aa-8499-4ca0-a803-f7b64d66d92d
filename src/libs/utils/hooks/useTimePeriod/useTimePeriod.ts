import { useState, useMemo } from 'react';
import dayjs, { type Dayjs } from 'dayjs';

const currentYear = dayjs().year();
const currentDate = dayjs();

export type PeriodOption =
  | 'entire year'
  | 'Q1'
  | 'Q2'
  | 'Q3'
  | 'Q4'
  | 'last month'
  | 'last quarter'
  | 'last 6 months';

interface TimePeriod {
  period: PeriodOption;
  startDate: Dayjs;
  endDate: Dayjs;
  setPeriod: (period: PeriodOption) => void;
  options: PeriodOption[];
  formattedRange: string;
}

interface UseTimePeriodOptions {
  defaultPeriod?: PeriodOption;
  availableOptions?: PeriodOption[];
}

export const useTimePeriod = (
  options: UseTimePeriodOptions = {},
): TimePeriod => {
  const {
    defaultPeriod = 'entire year',
    availableOptions = [
      'entire year',
      'Q1',
      'Q2',
      'Q3',
      'Q4',
      'last month',
      'last quarter',
      'last 6 months',
    ],
  } = options;

  const [period, setPeriod] = useState<PeriodOption>(defaultPeriod);

  const { startDate, endDate } = useMemo(() => {
    switch (period) {
      case 'Q1':
        return {
          startDate: dayjs(`${currentYear}-01-01`),
          endDate: dayjs(`${currentYear}-03-31`),
        };
      case 'Q2':
        return {
          startDate: dayjs(`${currentYear}-04-01`),
          endDate: dayjs(`${currentYear}-06-30`),
        };
      case 'Q3':
        return {
          startDate: dayjs(`${currentYear}-07-01`),
          endDate: dayjs(`${currentYear}-09-30`),
        };
      case 'Q4':
        return {
          startDate: dayjs(`${currentYear}-10-01`),
          endDate: dayjs(`${currentYear}-12-31`),
        };
      case 'last month':
        return {
          startDate: currentDate.subtract(1, 'month').startOf('month'),
          endDate: currentDate.subtract(1, 'month').endOf('month'),
        };
      case 'last quarter': {
        const lastQuarter = currentDate.subtract(3, 'month');
        const quarterStart = lastQuarter
          .startOf('month')
          .month(Math.floor(lastQuarter.month() / 3) * 3);
        return {
          startDate: quarterStart,
          endDate: quarterStart.add(2, 'month').endOf('month'),
        };
      }
      case 'last 6 months':
        return {
          startDate: currentDate.subtract(6, 'month'),
          endDate: currentDate,
        };
      case 'entire year':
      default:
        return {
          startDate: dayjs(`${currentYear}-01-01`),
          endDate: dayjs(`${currentYear}-12-31`),
        };
    }
  }, [period, currentYear, currentDate]);

  const formattedRange = useMemo(() => {
    return `${startDate.format('MM/DD/YYYY')} - ${endDate.format('MM/DD/YYYY')}`;
  }, [startDate, endDate]);

  return {
    period,
    startDate,
    endDate,
    setPeriod,
    options: availableOptions,
    formattedRange,
  };
};
