import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';
import path from 'path';

const APPS = ['shop', 'gpo-portal'];

export default defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  const app = process.env.APP || APPS[0];

  console.log(`Selected app: ${app}`);

  return {
    server: {
      port: 3000, // + APPS.indexOf(app),
      host: '0.0.0.0',
      open: true,
    },
    plugins: [react(), svgr(), tsconfigPaths()],
    test: {
      environment: 'jsdom',
      globals: true,
      setupFiles: path.resolve(__dirname, './vitest.setup.js'),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    root: path.resolve(__dirname, `src/apps/${app}`),
    base: './',
    publicDir: path.resolve(__dirname, './public'),
    build: {
      outDir: path.resolve(__dirname, `build/${app}`),
      sourcemap: process.env.NODE_ENV === 'staging',
      emptyOutDir: true,
    },
  };
});
