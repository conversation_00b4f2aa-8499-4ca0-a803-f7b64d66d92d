import '@mantine/core/styles.css';
import { ReactNode } from 'react';
import { ThemeProvider } from '../src/providers/ThemeProvider';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';

type Decorator = (renderStory: () => ReactNode) => ReactNode;

export const decorators: Decorator[] = [
  (renderStory) => <ThemeProvider>{renderStory()}</ThemeProvider>,
  (renderStory) => <BrowserRouter>{renderStory()}</BrowserRouter>,
];
