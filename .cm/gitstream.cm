manifest:
  version: 1.0

automations:
  add_estimated_time_to_review_label:
    if:
      - true
    run:
      - action: add-label@v1
        args:
          label: "{{ etr }} min review"
          color: {{ colors.red if (etr >= 20) else ( colors.yellow if (etr >= 5) else colors.green ) }}

  enforce_pr_title:
    if:
      - {{ pr.title | match(regex=r/\[\b[A-Z]{3}-\d+\b\]\s\w+.*/) | nope }}
    run:
      - action: request-changes@v1
        args:
          comment: |
            All PRs must be titled according to the following format:

            [ENG-123] Your PR title

            Please update the title of this PR to match the format.

etr: {{ branch | estimatedReviewTime }}

colors:
  red: b60205
  yellow: fbca04
  green: 0e8a16
